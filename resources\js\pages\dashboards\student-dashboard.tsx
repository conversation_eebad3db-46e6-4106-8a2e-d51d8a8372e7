import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DataTable, type Column } from '@/components/ui/data-table';
import { FinancialCard, GPACard, StatsCard, StatsGrid } from '@/components/ui/stats-card';
import { useDashboardData } from '@/hooks/use-api';
import { type Assignment, type Enrollment, type Grade, type StudentDashboardData } from '@/types';
import { router } from '@inertiajs/react';
import { BookOpen, Calendar, DollarSign, GraduationCap, TrendingUp } from 'lucide-react';

export function StudentDashboard() {
    const { data, loading, error } = useDashboardData();
    const dashboardData = data as StudentDashboardData;

    if (error) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="text-center">
                    <h3 className="text-lg font-medium text-red-600">Error loading dashboard</h3>
                    <p className="mt-2 text-sm text-gray-600">{error}</p>
                    <Button 
                        onClick={() => window.location.reload()} 
                        className="mt-4"
                        variant="outline"
                    >
                        Retry
                    </Button>
                </div>
            </div>
        );
    }

    // Define columns for enrollments table
    const enrollmentColumns: Column<Enrollment>[] = [
        {
            key: 'course.code',
            title: 'Course Code',
            width: '120px',
        },
        {
            key: 'course.name',
            title: 'Course Name',
        },
        {
            key: 'course.credit_hours',
            title: 'Credits',
            width: '80px',
            render: (value) => value || 'N/A',
        },
        {
            key: 'course.lecturer.name',
            title: 'Lecturer',
            render: (value) => value || 'TBA',
        },
        {
            key: 'status',
            title: 'Status',
            width: '100px',
            render: (value) => (
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    value === 'enrolled' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : value === 'completed'
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                }`}>
                    {value}
                </span>
            ),
        },
    ];

    // Define columns for assignments table
    const assignmentColumns: Column<Assignment>[] = [
        {
            key: 'title',
            title: 'Assignment',
        },
        {
            key: 'course.name',
            title: 'Course',
        },
        {
            key: 'due_date',
            title: 'Due Date',
            width: '120px',
            render: (value) => new Date(value).toLocaleDateString(),
        },
        {
            key: 'max_points',
            title: 'Points',
            width: '80px',
        },
        {
            key: 'status',
            title: 'Status',
            width: '100px',
            render: (value, record) => {
                const isOverdue = new Date(record.due_date) < new Date();
                return (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        isOverdue
                            ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    }`}>
                        {isOverdue ? 'Overdue' : 'Pending'}
                    </span>
                );
            },
        },
    ];

    // Define columns for recent grades table
    const gradeColumns: Column<Grade>[] = [
        {
            key: 'enrollment.course.name',
            title: 'Course',
        },
        {
            key: 'letter_grade',
            title: 'Grade',
            width: '80px',
            render: (value) => (
                <span className="font-medium">{value}</span>
            ),
        },
        {
            key: 'grade_point',
            title: 'GPA',
            width: '80px',
            render: (value) => value?.toFixed(2) || 'N/A',
        },
        {
            key: 'percentage',
            title: 'Percentage',
            width: '100px',
            render: (value) => value ? `${value.toFixed(1)}%` : 'N/A',
        },
        {
            key: 'graded_at',
            title: 'Date',
            width: '120px',
            render: (value) => new Date(value).toLocaleDateString(),
        },
    ];

    return (
        <div className="space-y-6">
            {/* Stats Overview */}
            <StatsGrid>
                <StatsCard
                    title="Enrolled Courses"
                    value={dashboardData?.stats?.total_courses || 0}
                    description="Current semester"
                    icon={BookOpen}
                    loading={loading}
                />
                <StatsCard
                    title="Pending Assignments"
                    value={dashboardData?.stats?.pending_assignments || 0}
                    description="Due soon"
                    icon={Calendar}
                    loading={loading}
                />
                <StatsCard
                    title="Completed Assignments"
                    value={dashboardData?.stats?.completed_assignments || 0}
                    description="Total submitted"
                    icon={GraduationCap}
                    loading={loading}
                />
                <StatsCard
                    title="Outstanding Balance"
                    value={dashboardData?.financial?.outstanding_balance 
                        ? `$${dashboardData.financial.outstanding_balance.toFixed(2)}` 
                        : '$0.00'
                    }
                    description="Current semester"
                    icon={DollarSign}
                    loading={loading}
                />
            </StatsGrid>

            {/* GPA and Financial Status */}
            <div className="grid gap-6 md:grid-cols-2">
                <GPACard
                    sgpa={dashboardData?.gpa?.sgpa}
                    cgpa={dashboardData?.gpa?.cgpa}
                    canAccess={dashboardData?.gpa?.can_access || false}
                    loading={loading}
                />
                <FinancialCard
                    outstandingBalance={dashboardData?.financial?.outstanding_balance || 0}
                    feeThreshold={dashboardData?.financial?.fee_threshold}
                    canAccessGrades={dashboardData?.financial?.can_access_grades || false}
                    loading={loading}
                />
            </div>

            {/* Current Enrollments */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Current Enrollments</CardTitle>
                    <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => router.visit('/student/courses')}
                    >
                        View All
                    </Button>
                </CardHeader>
                <CardContent>
                    <DataTable
                        data={dashboardData?.enrollments || []}
                        columns={enrollmentColumns}
                        loading={loading}
                        searchable={false}
                        emptyText="No enrollments found for current semester"
                    />
                </CardContent>
            </Card>

            {/* Pending Assignments */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Pending Assignments</CardTitle>
                    <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => router.visit('/student/assignments')}
                    >
                        View All
                    </Button>
                </CardHeader>
                <CardContent>
                    <DataTable
                        data={dashboardData?.pending_assignments || []}
                        columns={assignmentColumns}
                        loading={loading}
                        searchable={false}
                        emptyText="No pending assignments"
                    />
                </CardContent>
            </Card>

            {/* Recent Grades */}
            {dashboardData?.gpa?.can_access && (
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                        <CardTitle>Recent Grades</CardTitle>
                        <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => router.visit('/student/grades')}
                        >
                            View All
                        </Button>
                    </CardHeader>
                    <CardContent>
                        <DataTable
                            data={dashboardData?.recent_grades || []}
                            columns={gradeColumns}
                            loading={loading}
                            searchable={false}
                            emptyText="No grades available"
                        />
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
