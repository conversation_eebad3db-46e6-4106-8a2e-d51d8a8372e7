<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreGradeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check() && (
            Auth::user()->can('can:grade-submissions') ||
            Auth::user()->can('can:bulk-import-grades')
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'enrollment_id' => 'required|integer|exists:enrollments,id',
            'grade_point' => 'nullable|numeric|between:0,4.00',
            'letter_grade' => 'nullable|string|max:2',
            'percentage' => 'nullable|numeric|between:0,100',
            'comments' => 'nullable|string|max:1000',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'enrollment_id.required' => 'Enrollment ID is required.',
            'enrollment_id.exists' => 'The selected enrollment does not exist.',
            'grade_point.between' => 'Grade point must be between 0 and 4.00.',
            'percentage.between' => 'Percentage must be between 0 and 100.',
            'letter_grade.max' => 'Letter grade cannot exceed 2 characters.',
            'comments.max' => 'Comments cannot exceed 1000 characters.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Ensure at least one grading method is provided
            if (!$this->grade_point && !$this->percentage && !$this->letter_grade) {
                $validator->errors()->add('grade', 'At least one grading method (grade point, percentage, or letter grade) must be provided.');
            }
        });
    }
}
