<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\FeeThreshold;
use App\Models\Payment;
use App\Models\Semester;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class PaymentController extends Controller
{
    /**
     * Display a listing of payments.
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();

        $query = Payment::with(['student', 'semester', 'processedBy']);

        // Students can only see their own payments
        if ($user->isStudent()) {
            $query->where('user_id', $user->id);
        }

        // Filter by semester if provided
        if ($request->semester_id) {
            $query->where('semester_id', $request->semester_id);
        }

        // Filter by status if provided
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter by student if provided (for accountants/admins)
        if ($request->student_id && !$user->isStudent()) {
            $query->where('user_id', $request->student_id);
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json([
            'success' => true,
            'payments' => $payments
        ]);
    }

    /**
     * Store a newly created payment.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|integer|exists:users,id',
            'semester_id' => 'required|integer|exists:semesters,id',
            'amount' => 'required|numeric|min:0.01',
            'type' => ['required', Rule::in(['tuition', 'fees', 'penalty', 'other'])],
            'description' => 'nullable|string|max:255',
            'payment_method' => 'nullable|string|max:50',
            'transaction_id' => 'nullable|string|max:100|unique:payments,transaction_id',
            'reference_number' => 'nullable|string|max:100',
            'payment_date' => 'required|date',
            'due_date' => 'nullable|date|after_or_equal:payment_date',
            'notes' => 'nullable|string|max:1000',
        ]);

        $payment = Payment::create([
            'user_id' => $request->user_id,
            'semester_id' => $request->semester_id,
            'amount' => $request->amount,
            'type' => $request->type,
            'description' => $request->description,
            'status' => 'pending',
            'payment_method' => $request->payment_method,
            'transaction_id' => $request->transaction_id,
            'reference_number' => $request->reference_number,
            'payment_date' => $request->payment_date,
            'due_date' => $request->due_date,
            'processed_by' => Auth::id(),
            'notes' => $request->notes,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Payment created successfully.',
            'payment' => $payment->load(['student', 'semester'])
        ], 201);
    }

    /**
     * Display the specified payment.
     */
    public function show(Payment $payment): JsonResponse
    {
        $user = Auth::user();

        // Students can only view their own payments
        if ($user->isStudent() && $payment->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'You can only view your own payments.'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'payment' => $payment->load(['student', 'semester', 'processedBy'])
        ]);
    }

    /**
     * Update the specified payment.
     */
    public function update(Request $request, Payment $payment): JsonResponse
    {
        $request->validate([
            'amount' => 'sometimes|numeric|min:0.01',
            'type' => ['sometimes', Rule::in(['tuition', 'fees', 'penalty', 'other'])],
            'description' => 'nullable|string|max:255',
            'status' => ['sometimes', Rule::in(['pending', 'completed', 'failed', 'refunded'])],
            'payment_method' => 'nullable|string|max:50',
            'transaction_id' => 'nullable|string|max:100|unique:payments,transaction_id,' . $payment->id,
            'reference_number' => 'nullable|string|max:100',
            'payment_date' => 'sometimes|date',
            'due_date' => 'nullable|date',
            'notes' => 'nullable|string|max:1000',
        ]);

        $payment->update($request->only([
            'amount',
            'type',
            'description',
            'status',
            'payment_method',
            'transaction_id',
            'reference_number',
            'payment_date',
            'due_date',
            'notes'
        ]));

        return response()->json([
            'success' => true,
            'message' => 'Payment updated successfully.',
            'payment' => $payment->load(['student', 'semester'])
        ]);
    }

    /**
     * Remove the specified payment.
     */
    public function destroy(Payment $payment): JsonResponse
    {
        $payment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Payment deleted successfully.'
        ]);
    }

    /**
     * Get student's outstanding balance.
     */
    public function getStudentBalance(Request $request, int $studentId): JsonResponse
    {
        $request->validate([
            'semester_id' => 'required|integer|exists:semesters,id',
        ]);

        $user = Auth::user();

        // Students can only check their own balance
        if ($user->isStudent() && $user->id !== $studentId) {
            return response()->json([
                'success' => false,
                'message' => 'You can only check your own balance.'
            ], 403);
        }

        $outstandingBalance = Payment::getOutstandingBalance($studentId, $request->semester_id);
        $feeThreshold = FeeThreshold::getGradeAccessThreshold();
        $canAccessGrades = !FeeThreshold::studentExceedingGradeAccessThreshold($studentId, $request->semester_id);

        return response()->json([
            'success' => true,
            'data' => [
                'student_id' => $studentId,
                'semester_id' => $request->semester_id,
                'outstanding_balance' => $outstandingBalance,
                'fee_threshold' => $feeThreshold,
                'can_access_grades' => $canAccessGrades,
                'exceeds_threshold' => $outstandingBalance > ($feeThreshold ?? 0),
            ]
        ]);
    }
}
