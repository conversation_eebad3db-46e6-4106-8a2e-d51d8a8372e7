import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DataTable, type Column } from '@/components/ui/data-table';
import { StatsCard, StatsGrid } from '@/components/ui/stats-card';
import { useApi } from '@/hooks/use-api';
import { type Assignment, type Course, type LecturerDashboardData, type Submission } from '@/types';
import { router } from '@inertiajs/react';
import { BookOpen, Calendar, FileText, Users } from 'lucide-react';

export function LecturerDashboard() {
    const { data, loading, error } = useApi<LecturerDashboardData>('/api/lecturer/dashboard');

    if (error) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="text-center">
                    <h3 className="text-lg font-medium text-red-600">Error loading dashboard</h3>
                    <p className="mt-2 text-sm text-gray-600">{error}</p>
                    <Button 
                        onClick={() => window.location.reload()} 
                        className="mt-4"
                        variant="outline"
                    >
                        Retry
                    </Button>
                </div>
            </div>
        );
    }

    // Define columns for courses table
    const courseColumns: Column<Course>[] = [
        {
            key: 'code',
            title: 'Course Code',
            width: '120px',
        },
        {
            key: 'name',
            title: 'Course Name',
        },
        {
            key: 'credit_hours',
            title: 'Credits',
            width: '80px',
        },
        {
            key: 'enrolled_students_count',
            title: 'Students',
            width: '100px',
            render: (value) => value || 0,
        },
        {
            key: 'is_active',
            title: 'Status',
            width: '100px',
            render: (value) => (
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    value 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                }`}>
                    {value ? 'Active' : 'Inactive'}
                </span>
            ),
        },
        {
            key: 'actions',
            title: 'Actions',
            width: '120px',
            render: (_, record) => (
                <div className="flex space-x-2">
                    <Button
                        size="sm"
                        variant="outline"
                        onClick={() => router.visit(`/lecturer/courses/${record.id}`)}
                    >
                        Manage
                    </Button>
                </div>
            ),
        },
    ];

    // Define columns for pending submissions table
    const submissionColumns: Column<Submission>[] = [
        {
            key: 'student.name',
            title: 'Student',
        },
        {
            key: 'assignment.title',
            title: 'Assignment',
        },
        {
            key: 'assignment.course.name',
            title: 'Course',
        },
        {
            key: 'submitted_at',
            title: 'Submitted',
            width: '120px',
            render: (value) => new Date(value).toLocaleDateString(),
        },
        {
            key: 'is_late',
            title: 'Status',
            width: '100px',
            render: (value) => (
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    value
                        ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                }`}>
                    {value ? 'Late' : 'On Time'}
                </span>
            ),
        },
        {
            key: 'actions',
            title: 'Actions',
            width: '120px',
            render: (_, record) => (
                <Button
                    size="sm"
                    variant="outline"
                    onClick={() => router.visit(`/lecturer/submissions/${record.id}`)}
                >
                    Grade
                </Button>
            ),
        },
    ];

    // Define columns for recent assignments table
    const assignmentColumns: Column<Assignment>[] = [
        {
            key: 'title',
            title: 'Assignment',
        },
        {
            key: 'course.name',
            title: 'Course',
        },
        {
            key: 'due_date',
            title: 'Due Date',
            width: '120px',
            render: (value) => new Date(value).toLocaleDateString(),
        },
        {
            key: 'max_points',
            title: 'Points',
            width: '80px',
        },
        {
            key: 'submissions_count',
            title: 'Submissions',
            width: '100px',
            render: (value) => value || 0,
        },
        {
            key: 'status',
            title: 'Status',
            width: '100px',
            render: (value) => (
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    value === 'published'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : value === 'draft'
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                }`}>
                    {value}
                </span>
            ),
        },
    ];

    return (
        <div className="space-y-6">
            {/* Stats Overview */}
            <StatsGrid>
                <StatsCard
                    title="My Courses"
                    value={data?.stats?.total_courses || 0}
                    description="Current semester"
                    icon={BookOpen}
                    loading={loading}
                />
                <StatsCard
                    title="Total Students"
                    value={data?.stats?.total_students || 0}
                    description="Across all courses"
                    icon={Users}
                    loading={loading}
                />
                <StatsCard
                    title="Pending Submissions"
                    value={data?.stats?.pending_submissions || 0}
                    description="Need grading"
                    icon={FileText}
                    loading={loading}
                />
                <StatsCard
                    title="Assignments Created"
                    value={data?.stats?.assignments_created || 0}
                    description="This semester"
                    icon={Calendar}
                    loading={loading}
                />
            </StatsGrid>

            {/* My Courses */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>My Courses</CardTitle>
                    <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => router.visit('/lecturer/courses')}
                    >
                        Manage All
                    </Button>
                </CardHeader>
                <CardContent>
                    <DataTable
                        data={data?.my_courses || []}
                        columns={courseColumns}
                        loading={loading}
                        searchable={false}
                        emptyText="No courses assigned"
                    />
                </CardContent>
            </Card>

            {/* Pending Submissions */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Pending Submissions</CardTitle>
                    <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => router.visit('/lecturer/submissions')}
                    >
                        View All
                    </Button>
                </CardHeader>
                <CardContent>
                    <DataTable
                        data={data?.pending_submissions || []}
                        columns={submissionColumns}
                        loading={loading}
                        searchable={false}
                        emptyText="No pending submissions"
                    />
                </CardContent>
            </Card>

            {/* Recent Assignments */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Recent Assignments</CardTitle>
                    <div className="flex space-x-2">
                        <Button 
                            size="sm"
                            onClick={() => router.visit('/lecturer/assignments/create')}
                        >
                            Create Assignment
                        </Button>
                        <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => router.visit('/lecturer/assignments')}
                        >
                            View All
                        </Button>
                    </div>
                </CardHeader>
                <CardContent>
                    <DataTable
                        data={data?.recent_assignments || []}
                        columns={assignmentColumns}
                        loading={loading}
                        searchable={false}
                        emptyText="No assignments created yet"
                    />
                </CardContent>
            </Card>
        </div>
    );
}
