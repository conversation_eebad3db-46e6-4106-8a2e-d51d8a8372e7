<?php

use App\Http\Controllers\Api\AssignmentController;
use App\Http\Controllers\Api\CourseController;
use App\Http\Controllers\Api\DashboardController;
use App\Http\Controllers\Api\EnrollmentController;
use App\Http\Controllers\Api\FeeThresholdController;
use App\Http\Controllers\Api\GradeController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\SemesterController;
use App\Http\Controllers\Api\SubmissionController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Protected API routes
Route::middleware(['auth:sanctum'])->group(function () {
    
    // Dashboard routes
    Route::get('/dashboard', [DashboardController::class, 'index']);
    
    // Grade routes with fee threshold checking
    Route::prefix('grades')->group(function () {
        Route::get('/', [GradeController::class, 'index']); // Get student grades with fee check
        Route::post('/', [GradeController::class, 'store'])->middleware('permission:can:grade-submissions');
        Route::get('/student/{studentId}', [GradeController::class, 'show']); // Get student GPA info
        Route::put('/{grade}', [GradeController::class, 'update'])->middleware('permission:can:grade-submissions');
        Route::delete('/{grade}', [GradeController::class, 'destroy'])->middleware('permission:can:grade-submissions');
        Route::post('/bulk-import', [GradeController::class, 'bulkImport'])->middleware('permission:can:bulk-import-grades');
        Route::get('/course/{courseId}/statistics', [GradeController::class, 'courseStatistics'])->middleware('permission:can:view-course-students');
    });
    
    // Semester routes
    Route::middleware('permission:can:manage-semesters')->group(function () {
        Route::apiResource('semesters', SemesterController::class);
    });
    
    // Course routes
    Route::prefix('courses')->group(function () {
        Route::get('/', [CourseController::class, 'index']); // All users can view courses
        Route::post('/', [CourseController::class, 'store'])->middleware('permission:can:manage-courses');
        Route::get('/{course}', [CourseController::class, 'show']);
        Route::put('/{course}', [CourseController::class, 'update'])->middleware('permission:can:manage-courses');
        Route::delete('/{course}', [CourseController::class, 'destroy'])->middleware('permission:can:manage-courses');
        Route::get('/{course}/students', [CourseController::class, 'getStudents'])->middleware('permission:can:view-course-students');
    });
    
    // Enrollment routes
    Route::prefix('enrollments')->group(function () {
        Route::get('/', [EnrollmentController::class, 'index']);
        Route::post('/', [EnrollmentController::class, 'store'])->middleware('permission:can:manage-enrollments');
        Route::get('/{enrollment}', [EnrollmentController::class, 'show']);
        Route::put('/{enrollment}', [EnrollmentController::class, 'update'])->middleware('permission:can:manage-enrollments');
        Route::delete('/{enrollment}', [EnrollmentController::class, 'destroy'])->middleware('permission:can:manage-enrollments');
    });
    
    // Assignment routes
    Route::prefix('assignments')->group(function () {
        Route::get('/', [AssignmentController::class, 'index']); // Students can view published assignments
        Route::post('/', [AssignmentController::class, 'store'])->middleware('permission:can:manage-assignments');
        Route::get('/{assignment}', [AssignmentController::class, 'show']);
        Route::put('/{assignment}', [AssignmentController::class, 'update'])->middleware('permission:can:manage-assignments');
        Route::delete('/{assignment}', [AssignmentController::class, 'destroy'])->middleware('permission:can:manage-assignments');
        Route::get('/{assignment}/submissions', [AssignmentController::class, 'getSubmissions'])->middleware('permission:can:grade-submissions');
    });
    
    // Submission routes
    Route::prefix('submissions')->group(function () {
        Route::get('/', [SubmissionController::class, 'index']); // Students see their own, lecturers see course submissions
        Route::post('/', [SubmissionController::class, 'store'])->middleware('permission:can:submit-assignments');
        Route::get('/{submission}', [SubmissionController::class, 'show']);
        Route::put('/{submission}', [SubmissionController::class, 'update'])->middleware('permission:can:submit-assignments');
        Route::delete('/{submission}', [SubmissionController::class, 'destroy'])->middleware('permission:can:submit-assignments');
        Route::post('/{submission}/grade', [SubmissionController::class, 'grade'])->middleware('permission:can:grade-submissions');
    });
    
    // Payment routes
    Route::prefix('payments')->group(function () {
        Route::get('/', [PaymentController::class, 'index']); // Students see their own, accountants see all
        Route::post('/', [PaymentController::class, 'store'])->middleware('permission:can:manage-payments');
        Route::get('/{payment}', [PaymentController::class, 'show']);
        Route::put('/{payment}', [PaymentController::class, 'update'])->middleware('permission:can:manage-payments');
        Route::delete('/{payment}', [PaymentController::class, 'destroy'])->middleware('permission:can:manage-payments');
        Route::get('/student/{studentId}/balance', [PaymentController::class, 'getStudentBalance']);
        Route::post('/send-reminders', [PaymentController::class, 'sendReminders'])->middleware('permission:can:send-reminders');
    });
    
    // Fee Threshold routes
    Route::middleware('permission:can:set-fee-thresholds')->group(function () {
        Route::apiResource('fee-thresholds', FeeThresholdController::class);
    });
    
    // Role-specific routes
    Route::prefix('student')->middleware('role:student')->group(function () {
        Route::get('/my-courses', [CourseController::class, 'getMyCourses']);
        Route::get('/my-grades', [GradeController::class, 'getMyGrades']);
        Route::get('/my-assignments', [AssignmentController::class, 'getMyAssignments']);
        Route::get('/my-submissions', [SubmissionController::class, 'getMySubmissions']);
        Route::get('/my-payments', [PaymentController::class, 'getMyPayments']);
    });
    
    Route::prefix('lecturer')->middleware('role:lecturer')->group(function () {
        Route::get('/my-courses', [CourseController::class, 'getMyCourses']);
        Route::get('/my-assignments', [AssignmentController::class, 'getMyAssignments']);
        Route::get('/pending-submissions', [SubmissionController::class, 'getPendingSubmissions']);
    });
    
    Route::prefix('admin')->middleware('role:admin')->group(function () {
        Route::get('/reports/enrollments', [EnrollmentController::class, 'getEnrollmentReports']);
        Route::get('/reports/grades', [GradeController::class, 'getGradeReports']);
        Route::post('/bulk-operations/enrollments', [EnrollmentController::class, 'bulkEnroll']);
    });
    
    Route::prefix('accountant')->middleware('role:accountant')->group(function () {
        Route::get('/payment-reports', [PaymentController::class, 'getPaymentReports']);
        Route::get('/overdue-payments', [PaymentController::class, 'getOverduePayments']);
        Route::get('/students-exceeding-threshold', [PaymentController::class, 'getStudentsExceedingThreshold']);
    });
    
    Route::prefix('super-admin')->middleware('role:super_admin')->group(function () {
        Route::get('/system-stats', [DashboardController::class, 'getSystemStats']);
        Route::post('/users/{user}/assign-role', [DashboardController::class, 'assignRole']);
        Route::delete('/users/{user}/remove-role', [DashboardController::class, 'removeRole']);
    });
});
