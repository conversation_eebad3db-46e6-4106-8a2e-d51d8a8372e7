import { Card, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';
import { type ReactNode } from 'react';

interface StatsCardProps {
    title: string;
    value: string | number;
    description?: string;
    icon?: LucideIcon;
    trend?: {
        value: number;
        label: string;
        isPositive?: boolean;
    };
    loading?: boolean;
    className?: string;
    children?: ReactNode;
}

export function StatsCard({
    title,
    value,
    description,
    icon: Icon,
    trend,
    loading = false,
    className,
    children,
}: StatsCardProps) {
    if (loading) {
        return (
            <Card className={cn('', className)}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-4" />
                </CardHeader>
                <CardContent>
                    <Skeleton className="h-8 w-16 mb-2" />
                    <Skeleton className="h-3 w-32" />
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className={cn('', className)}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                    {title}
                </CardTitle>
                {Icon && (
                    <Icon className="h-4 w-4 text-muted-foreground" />
                )}
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold">{value}</div>
                {description && (
                    <p className="text-xs text-muted-foreground mt-1">
                        {description}
                    </p>
                )}
                {trend && (
                    <div className="flex items-center mt-2">
                        <span
                            className={cn(
                                'text-xs font-medium',
                                trend.isPositive
                                    ? 'text-green-600 dark:text-green-400'
                                    : 'text-red-600 dark:text-red-400'
                            )}
                        >
                            {trend.isPositive ? '+' : ''}{trend.value}%
                        </span>
                        <span className="text-xs text-muted-foreground ml-1">
                            {trend.label}
                        </span>
                    </div>
                )}
                {children}
            </CardContent>
        </Card>
    );
}

interface StatsGridProps {
    children: ReactNode;
    className?: string;
}

export function StatsGrid({ children, className }: StatsGridProps) {
    return (
        <div className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-4', className)}>
            {children}
        </div>
    );
}

// Specialized stats cards for common use cases
interface GPACardProps {
    sgpa?: number | null;
    cgpa?: number | null;
    canAccess: boolean;
    loading?: boolean;
    className?: string;
}

export function GPACard({ sgpa, cgpa, canAccess, loading = false, className }: GPACardProps) {
    if (loading) {
        return (
            <Card className={cn('', className)}>
                <CardHeader>
                    <CardTitle className="text-sm font-medium">GPA Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <Skeleton className="h-4 w-16 mb-1" />
                        <Skeleton className="h-8 w-12" />
                    </div>
                    <div>
                        <Skeleton className="h-4 w-16 mb-1" />
                        <Skeleton className="h-8 w-12" />
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (!canAccess) {
        return (
            <Card className={cn('border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950', className)}>
                <CardHeader>
                    <CardTitle className="text-sm font-medium text-amber-800 dark:text-amber-200">
                        GPA Information
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-sm text-amber-700 dark:text-amber-300">
                        GPA access restricted due to outstanding fees. Please clear your balance to view grades.
                    </p>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className={cn('', className)}>
            <CardHeader>
                <CardTitle className="text-sm font-medium">GPA Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <div>
                    <p className="text-xs text-muted-foreground">Semester GPA</p>
                    <p className="text-2xl font-bold">
                        {sgpa !== null ? sgpa.toFixed(2) : 'N/A'}
                    </p>
                </div>
                <div>
                    <p className="text-xs text-muted-foreground">Cumulative GPA</p>
                    <p className="text-2xl font-bold">
                        {cgpa !== null ? cgpa.toFixed(2) : 'N/A'}
                    </p>
                </div>
            </CardContent>
        </Card>
    );
}

interface FinancialCardProps {
    outstandingBalance: number;
    feeThreshold?: number | null;
    canAccessGrades: boolean;
    loading?: boolean;
    className?: string;
}

export function FinancialCard({
    outstandingBalance,
    feeThreshold,
    canAccessGrades,
    loading = false,
    className,
}: FinancialCardProps) {
    if (loading) {
        return (
            <Card className={cn('', className)}>
                <CardHeader>
                    <CardTitle className="text-sm font-medium">Financial Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <Skeleton className="h-4 w-24 mb-1" />
                        <Skeleton className="h-8 w-16" />
                    </div>
                    <Skeleton className="h-4 w-32" />
                </CardContent>
            </Card>
        );
    }

    const exceedsThreshold = feeThreshold && outstandingBalance > feeThreshold;

    return (
        <Card className={cn(
            exceedsThreshold 
                ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950'
                : 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950',
            className
        )}>
            <CardHeader>
                <CardTitle className={cn(
                    'text-sm font-medium',
                    exceedsThreshold
                        ? 'text-red-800 dark:text-red-200'
                        : 'text-green-800 dark:text-green-200'
                )}>
                    Financial Status
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <div>
                    <p className={cn(
                        'text-xs',
                        exceedsThreshold
                            ? 'text-red-600 dark:text-red-400'
                            : 'text-green-600 dark:text-green-400'
                    )}>
                        Outstanding Balance
                    </p>
                    <p className="text-2xl font-bold">
                        ${outstandingBalance.toFixed(2)}
                    </p>
                </div>
                {feeThreshold && (
                    <p className={cn(
                        'text-xs',
                        exceedsThreshold
                            ? 'text-red-700 dark:text-red-300'
                            : 'text-green-700 dark:text-green-300'
                    )}>
                        {exceedsThreshold
                            ? `Exceeds threshold of $${feeThreshold.toFixed(2)}`
                            : `Within threshold of $${feeThreshold.toFixed(2)}`
                        }
                    </p>
                )}
                {!canAccessGrades && (
                    <p className="text-xs text-red-700 dark:text-red-300 font-medium">
                        Grade access restricted
                    </p>
                )}
            </CardContent>
        </Card>
    );
}
