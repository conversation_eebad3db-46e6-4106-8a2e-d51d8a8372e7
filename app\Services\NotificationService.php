<?php

namespace App\Services;

use App\Models\Assignment;
use App\Models\Payment;
use App\Models\Semester;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Send assignment deadline reminders to students.
     */
    public function sendAssignmentDeadlineReminders(): array
    {
        $sent = 0;
        $errors = [];

        // Get assignments due in the next 24-48 hours
        $upcomingAssignments = Assignment::where('status', 'published')
            ->whereBetween('due_date', [now()->addDay(), now()->addDays(2)])
            ->with(['course.enrollments.student'])
            ->get();

        foreach ($upcomingAssignments as $assignment) {
            foreach ($assignment->course->enrollments as $enrollment) {
                if ($enrollment->status === 'enrolled') {
                    try {
                        $this->sendAssignmentReminderNotification(
                            $enrollment->student,
                            $assignment
                        );
                        $sent++;
                    } catch (\Exception $e) {
                        $errors[] = "Failed to send reminder to {$enrollment->student->email}: " . $e->getMessage();
                        Log::error('Assignment reminder failed', [
                            'student_id' => $enrollment->student->id,
                            'assignment_id' => $assignment->id,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }
        }

        return [
            'success' => $sent > 0,
            'sent' => $sent,
            'errors' => $errors,
            'message' => "Sent {$sent} assignment deadline reminders" . (count($errors) > 0 ? " with " . count($errors) . " errors." : ".")
        ];
    }

    /**
     * Send payment reminders to students with overdue payments.
     */
    public function sendPaymentReminders(): array
    {
        $sent = 0;
        $errors = [];

        // Get overdue payments
        $overduePayments = Payment::where('status', 'pending')
            ->where('due_date', '<', now())
            ->with(['student', 'semester'])
            ->get();

        $groupedPayments = $overduePayments->groupBy('user_id');

        foreach ($groupedPayments as $studentId => $payments) {
            $student = $payments->first()->student;
            $totalOverdue = $payments->sum('amount');

            try {
                $this->sendPaymentReminderNotification($student, $payments, $totalOverdue);
                $sent++;
            } catch (\Exception $e) {
                $errors[] = "Failed to send payment reminder to {$student->email}: " . $e->getMessage();
                Log::error('Payment reminder failed', [
                    'student_id' => $studentId,
                    'total_overdue' => $totalOverdue,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'success' => $sent > 0,
            'sent' => $sent,
            'errors' => $errors,
            'message' => "Sent {$sent} payment reminders" . (count($errors) > 0 ? " with " . count($errors) . " errors." : ".")
        ];
    }

    /**
     * Send grade update notifications to students.
     */
    public function sendGradeUpdateNotifications(array $studentIds): array
    {
        $sent = 0;
        $errors = [];

        foreach ($studentIds as $studentId) {
            try {
                $student = User::find($studentId);
                if ($student && $student->isStudent()) {
                    $this->sendGradeUpdateNotification($student);
                    $sent++;
                }
            } catch (\Exception $e) {
                $errors[] = "Failed to send grade update notification to student ID {$studentId}: " . $e->getMessage();
                Log::error('Grade update notification failed', [
                    'student_id' => $studentId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'success' => $sent > 0,
            'sent' => $sent,
            'errors' => $errors,
            'message' => "Sent {$sent} grade update notifications" . (count($errors) > 0 ? " with " . count($errors) . " errors." : ".")
        ];
    }

    /**
     * Send new submission notifications to lecturers.
     */
    public function sendNewSubmissionNotifications(int $assignmentId): array
    {
        $sent = 0;
        $errors = [];

        try {
            $assignment = Assignment::with('course.lecturer')->find($assignmentId);
            
            if ($assignment && $assignment->course->lecturer) {
                $this->sendNewSubmissionNotification($assignment->course->lecturer, $assignment);
                $sent++;
            }
        } catch (\Exception $e) {
            $errors[] = "Failed to send submission notification: " . $e->getMessage();
            Log::error('New submission notification failed', [
                'assignment_id' => $assignmentId,
                'error' => $e->getMessage()
            ]);
        }

        return [
            'success' => $sent > 0,
            'sent' => $sent,
            'errors' => $errors,
            'message' => $sent > 0 ? "Sent submission notification to lecturer." : "Failed to send notification."
        ];
    }

    /**
     * Send assignment reminder notification to a student.
     */
    private function sendAssignmentReminderNotification(User $student, Assignment $assignment): void
    {
        // In a real application, this would send an email, push notification, etc.
        // For now, we'll log it and could integrate with Laravel's notification system
        
        $data = [
            'type' => 'assignment_deadline',
            'student_id' => $student->id,
            'assignment_id' => $assignment->id,
            'course_name' => $assignment->course->name,
            'assignment_title' => $assignment->title,
            'due_date' => $assignment->due_date,
            'message' => "Assignment '{$assignment->title}' for {$assignment->course->name} is due on {$assignment->due_date->format('M j, Y \a\t g:i A')}."
        ];

        Log::info('Assignment deadline reminder sent', $data);
        
        // TODO: Integrate with React Hot Toast on frontend
        // TODO: Send email notification
        // TODO: Send push notification if implemented
    }

    /**
     * Send payment reminder notification to a student.
     */
    private function sendPaymentReminderNotification(User $student, $payments, float $totalOverdue): void
    {
        $data = [
            'type' => 'payment_reminder',
            'student_id' => $student->id,
            'total_overdue' => $totalOverdue,
            'payment_count' => $payments->count(),
            'message' => "You have {$payments->count()} overdue payment(s) totaling $" . number_format($totalOverdue, 2) . ". Please settle your dues to avoid restrictions."
        ];

        Log::info('Payment reminder sent', $data);
        
        // TODO: Integrate with React Hot Toast on frontend
        // TODO: Send email notification
        // TODO: Send SMS if phone number available
    }

    /**
     * Send grade update notification to a student.
     */
    private function sendGradeUpdateNotification(User $student): void
    {
        $data = [
            'type' => 'grade_update',
            'student_id' => $student->id,
            'message' => "New grades have been posted. Check your student portal to view your updated grades and GPA."
        ];

        Log::info('Grade update notification sent', $data);
        
        // TODO: Integrate with React Hot Toast on frontend
        // TODO: Send email notification
    }

    /**
     * Send new submission notification to a lecturer.
     */
    private function sendNewSubmissionNotification(User $lecturer, Assignment $assignment): void
    {
        $data = [
            'type' => 'new_submission',
            'lecturer_id' => $lecturer->id,
            'assignment_id' => $assignment->id,
            'course_name' => $assignment->course->name,
            'assignment_title' => $assignment->title,
            'message' => "A new submission has been received for assignment '{$assignment->title}' in {$assignment->course->name}."
        ];

        Log::info('New submission notification sent', $data);
        
        // TODO: Integrate with React Hot Toast on frontend
        // TODO: Send email notification
    }

    /**
     * Get notification preferences for a user.
     */
    public function getNotificationPreferences(int $userId): array
    {
        // TODO: Implement user notification preferences
        // For now, return default preferences
        return [
            'assignment_deadlines' => true,
            'payment_reminders' => true,
            'grade_updates' => true,
            'new_submissions' => true,
            'email_notifications' => true,
            'push_notifications' => true,
        ];
    }

    /**
     * Update notification preferences for a user.
     */
    public function updateNotificationPreferences(int $userId, array $preferences): bool
    {
        // TODO: Implement user notification preferences storage
        // This would typically save to a user_notification_preferences table
        
        Log::info('Notification preferences updated', [
            'user_id' => $userId,
            'preferences' => $preferences
        ]);
        
        return true;
    }
}
