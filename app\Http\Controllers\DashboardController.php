<?php

namespace App\Http\Controllers;

use App\Models\Assignment;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\FeeThreshold;
use App\Models\Grade;
use App\Models\Payment;
use App\Models\Semester;
use App\Models\Submission;
use App\Models\User;
use App\Services\GradeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    protected GradeService $gradeService;

    public function __construct(GradeService $gradeService)
    {
        $this->gradeService = $gradeService;
    }

    /**
     * Show the dashboard with role-specific data.
     */
    public function index(Request $request): Response
    {
        $user = Auth::user();

        if ($user->isStudent()) {
            return $this->getStudentDashboard($user);
        } elseif ($user->isLecturer()) {
            return $this->getLecturerDashboard($user);
        } elseif ($user->isAdmin()) {
            return $this->getAdminDashboard($user);
        } elseif ($user->isAccountant()) {
            return $this->getAccountantDashboard($user);
        } elseif ($user->isSuperAdmin()) {
            return $this->getSuperAdminDashboard($user);
        }

        // Default dashboard for users without specific roles
        return Inertia::render('dashboard', [
            'dashboardData' => [
                'user' => $user->only(['id', 'name', 'email']),
                'message' => 'Welcome to the Student Portal. Your role has not been assigned yet.',
            ]
        ]);
    }

    /**
     * Get student dashboard data.
     */
    private function getStudentDashboard(User $student): Response
    {
        $currentSemester = Semester::current()->first();

        if (!$currentSemester) {
            return Inertia::render('dashboard', [
                'error' => 'No active semester found.',
                'dashboardData' => null
            ]);
        }

        // Get current enrollments
        $enrollments = Enrollment::where('user_id', $student->id)
            ->where('semester_id', $currentSemester->id)
            ->where('status', 'enrolled')
            ->with(['course.lecturer', 'grade'])
            ->get();

        // Get pending assignments
        $courseIds = $enrollments->pluck('course_id');
        $pendingAssignments = Assignment::whereIn('course_id', $courseIds)
            ->where('status', 'published')
            ->where('due_date', '>', now())
            ->with('course')
            ->orderBy('due_date', 'asc')
            ->limit(5)
            ->get();

        // Get recent grades (if fee threshold allows)
        $canAccessGrades = $student->canAccessGrades($currentSemester->id);
        $recentGrades = [];
        if ($canAccessGrades) {
            $recentGrades = Grade::whereHas('enrollment', function ($query) use ($student, $currentSemester) {
                $query->where('user_id', $student->id)
                    ->where('semester_id', $currentSemester->id);
            })
                ->with(['enrollment.course'])
                ->orderBy('graded_at', 'desc')
                ->limit(5)
                ->get();
        }

        // Get GPA information
        $sgpa = $canAccessGrades ? $student->getSGPA($currentSemester->id) : null;
        $cgpa = $canAccessGrades ? $student->getCGPA() : null;

        // Get outstanding balance
        $outstandingBalance = $student->getOutstandingBalance($currentSemester->id);
        $feeThreshold = FeeThreshold::getGradeAccessThreshold();

        return Inertia::render('dashboard', [
            'dashboardData' => [
                'user' => $student->only(['id', 'name', 'email', 'student_id']),
                'current_semester' => $currentSemester,
                'enrollments' => $enrollments,
                'pending_assignments' => $pendingAssignments,
                'recent_grades' => $recentGrades,
                'gpa' => [
                    'sgpa' => $sgpa,
                    'cgpa' => $cgpa,
                    'can_access' => $canAccessGrades,
                ],
                'financial' => [
                    'outstanding_balance' => $outstandingBalance,
                    'fee_threshold' => $feeThreshold,
                    'can_access_grades' => $canAccessGrades,
                ],
                'stats' => [
                    'total_courses' => $enrollments->count(),
                    'pending_assignments' => $pendingAssignments->count(),
                    'completed_assignments' => Submission::where('user_id', $student->id)->count(),
                ],
            ]
        ]);
    }

    /**
     * Get lecturer dashboard data.
     */
    private function getLecturerDashboard(User $lecturer): Response
    {
        $currentSemester = Semester::current()->first();

        if (!$currentSemester) {
            return Inertia::render('dashboard', [
                'error' => 'No active semester found.',
                'dashboardData' => null
            ]);
        }

        // Get courses taught by lecturer
        $courses = Course::where('lecturer_id', $lecturer->id)
            ->where('semester_id', $currentSemester->id)
            ->where('is_active', true)
            ->withCount(['enrollments as enrolled_students_count' => function ($query) {
                $query->where('status', 'enrolled');
            }])
            ->get();

        // Get recent assignments
        $recentAssignments = Assignment::whereIn('course_id', $courses->pluck('id'))
            ->with('course')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get pending submissions
        $pendingSubmissions = Submission::whereHas('assignment', function ($query) use ($courses) {
            $query->whereIn('course_id', $courses->pluck('id'));
        })
            ->whereNull('graded_at')
            ->with(['assignment.course', 'student'])
            ->orderBy('submitted_at', 'asc')
            ->limit(10)
            ->get();

        return Inertia::render('dashboard', [
            'dashboardData' => [
                'user' => $lecturer->only(['id', 'name', 'email']),
                'current_semester' => $currentSemester,
                'my_courses' => $courses,
                'recent_assignments' => $recentAssignments,
                'pending_submissions' => $pendingSubmissions,
                'stats' => [
                    'total_courses' => $courses->count(),
                    'total_students' => $courses->sum('enrolled_students_count'),
                    'assignments_created' => Assignment::whereIn('course_id', $courses->pluck('id'))->count(),
                    'pending_submissions' => $pendingSubmissions->count(),
                ],
            ]
        ]);
    }

    /**
     * Get admin dashboard data.
     */
    private function getAdminDashboard(User $admin): Response
    {
        $currentSemester = Semester::current()->first();

        // Get system statistics
        $totalStudents = User::role('student')->count();
        $totalLecturers = User::role('lecturer')->count();
        $totalCourses = Course::where('is_active', true)->count();
        $totalEnrollments = Enrollment::where('status', 'enrolled')->count();

        // Get recent enrollments
        $recentEnrollments = Enrollment::with(['user', 'course', 'semester'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get course statistics
        $courseStatistics = Course::where('is_active', true)
            ->with('lecturer')
            ->withCount(['enrollments as enrollment_count' => function ($query) {
                $query->where('status', 'enrolled');
            }])
            ->orderBy('enrollment_count', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($course) {
                return [
                    'course' => $course,
                    'enrollment_count' => $course->enrollment_count,
                ];
            });

        return Inertia::render('dashboard', [
            'dashboardData' => [
                'user' => $admin->only(['id', 'name', 'email']),
                'current_semester' => $currentSemester,
                'recent_enrollments' => $recentEnrollments,
                'course_statistics' => $courseStatistics,
                'stats' => [
                    'total_students' => $totalStudents,
                    'total_lecturers' => $totalLecturers,
                    'total_courses' => $totalCourses,
                    'total_enrollments' => $totalEnrollments,
                ],
            ]
        ]);
    }

    /**
     * Get accountant dashboard data.
     */
    private function getAccountantDashboard(User $accountant): Response
    {
        $currentSemester = Semester::current()->first();

        // Get payment statistics
        $totalPayments = Payment::where('status', 'completed')->sum('amount');
        $outstandingBalance = Payment::where('status', 'pending')->sum('amount');
        $studentsExceedingThreshold = 0;
        $paymentCompletionRate = 0;

        if ($currentSemester) {
            $totalStudents = User::role('student')->count();
            $studentsWithPayments = Payment::where('semester_id', $currentSemester->id)
                ->where('status', 'completed')
                ->distinct('user_id')
                ->count();
            
            $paymentCompletionRate = $totalStudents > 0 ? ($studentsWithPayments / $totalStudents) * 100 : 0;

            // Count students exceeding threshold
            $feeThreshold = FeeThreshold::getGradeAccessThreshold();
            if ($feeThreshold) {
                $students = User::role('student')->get();
                foreach ($students as $student) {
                    $balance = $student->getOutstandingBalance($currentSemester->id);
                    if ($balance > $feeThreshold) {
                        $studentsExceedingThreshold++;
                    }
                }
            }
        }

        // Get recent payments
        $recentPayments = Payment::with(['user', 'semester'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get overdue payments
        $overduePayments = Payment::with(['user', 'semester'])
            ->where('status', 'pending')
            ->where('created_at', '<', now()->subDays(30)) // Consider 30+ days as overdue
            ->orderBy('created_at', 'asc')
            ->limit(10)
            ->get();

        return Inertia::render('dashboard', [
            'dashboardData' => [
                'user' => $accountant->only(['id', 'name', 'email']),
                'current_semester' => $currentSemester,
                'recent_payments' => $recentPayments,
                'overdue_payments' => $overduePayments,
                'financial_stats' => [
                    'total_payments' => $totalPayments,
                    'outstanding_balance' => $outstandingBalance,
                    'students_exceeding_threshold' => $studentsExceedingThreshold,
                    'payment_completion_rate' => $paymentCompletionRate,
                ],
            ]
        ]);
    }

    /**
     * Get super admin dashboard data.
     */
    private function getSuperAdminDashboard(User $superAdmin): Response
    {
        // Get comprehensive system statistics
        $stats = [
            'total_users' => User::count(),
            'total_students' => User::role('student')->count(),
            'total_lecturers' => User::role('lecturer')->count(),
            'total_admins' => User::role('admin')->count(),
            'total_accountants' => User::role('accountant')->count(),
            'total_courses' => Course::count(),
            'total_enrollments' => Enrollment::count(),
            'total_payments' => Payment::sum('amount'),
            'system_health' => 'good', // This could be determined by various system checks
        ];

        return Inertia::render('dashboard', [
            'dashboardData' => $stats
        ]);
    }
}
