import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type User } from '@/types';
import { Head, usePage } from '@inertiajs/react';
import { AccountantDashboard } from './dashboards/accountant-dashboard';
import { AdminDashboard } from './dashboards/admin-dashboard';
import { LecturerDashboard } from './dashboards/lecturer-dashboard';
import { StudentDashboard } from './dashboards/student-dashboard';
import { SuperAdminDashboard } from './dashboards/super-admin-dashboard';

interface DashboardProps {
    dashboardData?: any;
    error?: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard({ dashboardData, error }: DashboardProps) {
    const { auth } = usePage().props as { auth: { user: User } };
    const user = auth.user;

    // Get user's primary role
    const primaryRole = user.roles?.[0]?.name || user.primary_role;

    // Handle error state
    if (error) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Dashboard" />
                <div className="flex h-64 items-center justify-center">
                    <div className="text-center">
                        <h3 className="text-lg font-medium text-red-600">Error</h3>
                        <p className="mt-2 text-sm text-gray-600">{error}</p>
                    </div>
                </div>
            </AppLayout>
        );
    }

    const renderDashboard = () => {
        if (!dashboardData) {
            return (
                <div className="flex h-64 items-center justify-center">
                    <div className="text-center">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Welcome to the Student Portal</h3>
                        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                            Your role has not been assigned yet. Please contact the administrator.
                        </p>
                    </div>
                </div>
            );
        }

        switch (primaryRole) {
            case 'student':
                return <StudentDashboard data={dashboardData} />;
            case 'lecturer':
                return <LecturerDashboard data={dashboardData} />;
            case 'admin':
                return <AdminDashboard data={dashboardData} />;
            case 'accountant':
                return <AccountantDashboard data={dashboardData} />;
            case 'super_admin':
                return <SuperAdminDashboard data={dashboardData} />;
            default:
                return (
                    <div className="flex h-64 items-center justify-center">
                        <div className="text-center">
                            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Welcome to the Student Portal</h3>
                            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                                Your role has not been assigned yet. Please contact the administrator.
                            </p>
                        </div>
                    </div>
                );
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Welcome back, {user.name}!</h1>
                        <p className="text-muted-foreground">
                            {primaryRole && `${primaryRole.charAt(0).toUpperCase() + primaryRole.slice(1)} Dashboard`}
                        </p>
                    </div>
                </div>

                {renderDashboard()}
            </div>
        </AppLayout>
    );
}
