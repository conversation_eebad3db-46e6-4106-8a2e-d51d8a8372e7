<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\BulkGradeImportRequest;
use App\Http\Requests\StoreGradeRequest;
use App\Models\Enrollment;
use App\Models\Grade;
use App\Services\GradeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GradeController extends Controller
{
    protected GradeService $gradeService;

    public function __construct(GradeService $gradeService)
    {
        $this->gradeService = $gradeService;
    }

    /**
     * Get student grades for a specific semester with fee threshold check.
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'student_id' => 'required|integer|exists:users,id',
            'semester_id' => 'required|integer|exists:semesters,id',
        ]);

        $result = $this->gradeService->getStudentGrades(
            $request->student_id,
            $request->semester_id
        );

        return response()->json($result, $result['success'] ? 200 : 403);
    }

    /**
     * Store a newly created grade.
     */
    public function store(StoreGradeRequest $request): JsonResponse
    {
        $enrollment = Enrollment::findOrFail($request->enrollment_id);

        // Check if lecturer has permission to grade this course
        if (Auth::user()->isLecturer() && $enrollment->course->lecturer_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'You can only grade students in your own courses.'
            ], 403);
        }

        $gradeData = $request->validated();

        // Convert percentage to grade point if percentage is provided
        if (isset($gradeData['percentage'])) {
            $gradeInfo = Grade::percentageToGradePoint($gradeData['percentage']);
            $gradeData['grade_point'] = $gradeInfo['grade_point'];
            $gradeData['letter_grade'] = $gradeInfo['letter_grade'];
        }

        $gradeData['graded_by'] = Auth::id();
        $gradeData['graded_at'] = now();

        $grade = Grade::updateOrCreate(
            ['enrollment_id' => $request->enrollment_id],
            $gradeData
        );

        // Update enrollment status to completed
        $enrollment->update(['status' => 'completed']);

        return response()->json([
            'success' => true,
            'message' => 'Grade saved successfully.',
            'grade' => $grade->load(['enrollment.student', 'enrollment.course'])
        ], 201);
    }

    /**
     * Display student's GPA information.
     */
    public function show(Request $request, int $studentId): JsonResponse
    {
        $request->validate([
            'semester_id' => 'nullable|integer|exists:semesters,id',
        ]);

        if ($request->semester_id) {
            // Check fee threshold for specific semester
            $result = $this->gradeService->getStudentGrades($studentId, $request->semester_id);
            return response()->json($result, $result['success'] ? 200 : 403);
        }

        // Get overall GPA information
        $result = $this->gradeService->updateStudentGPA($studentId);
        return response()->json($result, $result['success'] ? 200 : 404);
    }

    /**
     * Bulk import grades from Excel/CSV.
     */
    public function bulkImport(BulkGradeImportRequest $request): JsonResponse
    {
        $gradesData = $request->validated()['grades'];

        $result = $this->gradeService->bulkImportGrades($gradesData, Auth::id());

        return response()->json($result, $result['success'] ? 200 : 422);
    }

    /**
     * Get course grade statistics (for lecturers/admins).
     */
    public function courseStatistics(Request $request, int $courseId): JsonResponse
    {
        $request->validate([
            'course_id' => 'required|integer|exists:courses,id',
        ]);

        $result = $this->gradeService->getCourseGradeStatistics($courseId);

        return response()->json([
            'success' => true,
            'statistics' => $result
        ]);
    }

    /**
     * Update the specified grade.
     */
    public function update(StoreGradeRequest $request, Grade $grade): JsonResponse
    {
        // Check if lecturer has permission to update this grade
        if (Auth::user()->isLecturer() && $grade->enrollment->course->lecturer_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'You can only update grades for your own courses.'
            ], 403);
        }

        $gradeData = $request->validated();

        // Convert percentage to grade point if percentage is provided
        if (isset($gradeData['percentage'])) {
            $gradeInfo = Grade::percentageToGradePoint($gradeData['percentage']);
            $gradeData['grade_point'] = $gradeInfo['grade_point'];
            $gradeData['letter_grade'] = $gradeInfo['letter_grade'];
        }

        $gradeData['graded_by'] = Auth::id();
        $gradeData['graded_at'] = now();

        $grade->update($gradeData);

        return response()->json([
            'success' => true,
            'message' => 'Grade updated successfully.',
            'grade' => $grade->load(['enrollment.student', 'enrollment.course'])
        ]);
    }

    /**
     * Remove the specified grade.
     */
    public function destroy(Grade $grade): JsonResponse
    {
        // Check if lecturer has permission to delete this grade
        if (Auth::user()->isLecturer() && $grade->enrollment->course->lecturer_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'You can only delete grades for your own courses.'
            ], 403);
        }

        // Update enrollment status back to enrolled
        $grade->enrollment->update(['status' => 'enrolled']);

        $grade->delete();

        return response()->json([
            'success' => true,
            'message' => 'Grade deleted successfully.'
        ]);
    }
}
