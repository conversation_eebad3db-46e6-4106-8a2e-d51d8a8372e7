import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    [key: string]: unknown;
}

export interface User {
    id: number;
    name: string;
    email: string;
    student_id?: string;
    phone?: string;
    address?: string;
    date_of_birth?: string;
    profile_photo?: string;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    roles?: Role[];
    primary_role?: string;
}

export interface Role {
    id: number;
    name: string;
    guard_name: string;
    created_at: string;
    updated_at: string;
}

export interface Permission {
    id: number;
    name: string;
    guard_name: string;
    created_at: string;
    updated_at: string;
}

export interface Semester {
    id: number;
    name: string;
    code: string;
    start_date: string;
    end_date: string;
    is_active: boolean;
    description?: string;
    created_at: string;
    updated_at: string;
}

export interface Course {
    id: number;
    code: string;
    name: string;
    description?: string;
    credit_hours: number;
    semester_id: number;
    lecturer_id: number;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    semester?: Semester;
    lecturer?: User;
    enrolled_students_count?: number;
}

export interface Enrollment {
    id: number;
    user_id: number;
    course_id: number;
    semester_id: number;
    status: 'enrolled' | 'dropped' | 'completed';
    enrolled_at: string;
    dropped_at?: string;
    created_at: string;
    updated_at: string;
    user?: User;
    course?: Course;
    semester?: Semester;
    grade?: Grade;
}

export interface Grade {
    id: number;
    enrollment_id: number;
    grade_point: number;
    letter_grade: string;
    percentage: number;
    comments?: string;
    graded_by: number;
    graded_at: string;
    created_at: string;
    updated_at: string;
    enrollment?: Enrollment;
    grader?: User;
}

export interface Assignment {
    id: number;
    course_id: number;
    title: string;
    description?: string;
    instructions?: string;
    max_points: number;
    due_date: string;
    available_from: string;
    allow_late_submission: boolean;
    late_penalty_percent: number;
    attachment_path?: string;
    status: 'draft' | 'published' | 'closed';
    created_by: number;
    created_at: string;
    updated_at: string;
    course?: Course;
    creator?: User;
    submissions_count?: number;
    is_overdue?: boolean;
}

export interface Submission {
    id: number;
    assignment_id: number;
    user_id: number;
    content?: string;
    attachment_path?: string;
    submitted_at: string;
    is_late: boolean;
    points_earned?: number;
    feedback?: string;
    graded_at?: string;
    graded_by?: number;
    created_at: string;
    updated_at: string;
    assignment?: Assignment;
    student?: User;
    grader?: User;
}

export interface Payment {
    id: number;
    user_id: number;
    semester_id: number;
    amount: number;
    payment_type: 'tuition' | 'fees' | 'other';
    payment_method: 'cash' | 'bank_transfer' | 'card' | 'online';
    reference_number?: string;
    description?: string;
    status: 'pending' | 'completed' | 'failed' | 'refunded';
    paid_at?: string;
    created_at: string;
    updated_at: string;
    user?: User;
    semester?: Semester;
}

export interface FeeThreshold {
    id: number;
    threshold_type: 'grade_access' | 'enrollment_block';
    amount: number;
    description?: string;
    is_active: boolean;
    created_at: string;
    updated_at: string;
}

// Dashboard Data Types
export interface StudentDashboardData {
    user: User;
    current_semester: Semester;
    enrollments: Enrollment[];
    pending_assignments: Assignment[];
    recent_grades: Grade[];
    gpa: {
        sgpa: number | null;
        cgpa: number | null;
        can_access: boolean;
    };
    financial: {
        outstanding_balance: number;
        fee_threshold: number | null;
        can_access_grades: boolean;
    };
    stats: {
        total_courses: number;
        pending_assignments: number;
        completed_assignments: number;
    };
}

export interface LecturerDashboardData {
    user: User;
    current_semester: Semester;
    my_courses: Course[];
    pending_submissions: Submission[];
    recent_assignments: Assignment[];
    stats: {
        total_courses: number;
        total_students: number;
        pending_submissions: number;
        assignments_created: number;
    };
}

export interface AdminDashboardData {
    current_semester: Semester;
    stats: {
        total_students: number;
        total_lecturers: number;
        total_courses: number;
        total_enrollments: number;
    };
    recent_enrollments: Enrollment[];
    course_statistics: Array<{
        course: Course;
        enrollment_count: number;
    }>;
}

export interface AccountantDashboardData {
    current_semester: Semester;
    financial_stats: {
        total_payments: number;
        outstanding_balance: number;
        students_exceeding_threshold: number;
        payment_completion_rate: number;
    };
    recent_payments: Payment[];
    overdue_payments: Payment[];
}

// API Response Types
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T = any> {
    data: T[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

// Form Data Types
export interface LoginForm {
    email: string;
    password: string;
    remember: boolean;
}

export interface RegisterForm {
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
}

export interface ProfileUpdateForm {
    name: string;
    email: string;
    student_id?: string;
    phone?: string;
    address?: string;
    date_of_birth?: string;
}

export interface CourseForm {
    code: string;
    name: string;
    description?: string;
    credit_hours: number;
    semester_id: number;
    lecturer_id: number;
    is_active: boolean;
}

export interface AssignmentForm {
    course_id: number;
    title: string;
    description?: string;
    instructions?: string;
    max_points: number;
    due_date: string;
    available_from: string;
    allow_late_submission: boolean;
    late_penalty_percent: number;
    status: 'draft' | 'published';
}

export interface GradeForm {
    enrollment_id: number;
    grade_point: number;
    letter_grade: string;
    percentage: number;
    comments?: string;
}

export interface PaymentForm {
    user_id: number;
    semester_id: number;
    amount: number;
    payment_type: 'tuition' | 'fees' | 'other';
    payment_method: 'cash' | 'bank_transfer' | 'card' | 'online';
    reference_number?: string;
    description?: string;
}
