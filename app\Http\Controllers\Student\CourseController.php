<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Models\Enrollment;
use App\Models\Semester;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class CourseController extends Controller
{
    /**
     * Display the student's courses.
     */
    public function index(Request $request): Response
    {
        $user = Auth::user();
        $currentSemester = Semester::current()->first();

        if (!$currentSemester) {
            return Inertia::render('student/courses', [
                'error' => 'No active semester found.',
                'enrollments' => [],
                'stats' => []
            ]);
        }

        // Get all enrollments for the student
        $enrollments = Enrollment::where('user_id', $user->id)
            ->with(['course.lecturer', 'course.semester', 'grade'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Calculate statistics
        $stats = [
            'active_courses' => $enrollments->where('status', 'enrolled')->count(),
            'completed_courses' => $enrollments->where('status', 'completed')->count(),
            'dropped_courses' => $enrollments->where('status', 'dropped')->count(),
            'total_credits' => $enrollments->where('status', 'enrolled')->sum(function ($enrollment) {
                return $enrollment->course->credit_hours ?? 0;
            }),
        ];

        return Inertia::render('student/courses', [
            'enrollments' => $enrollments,
            'stats' => $stats,
            'current_semester' => $currentSemester,
        ]);
    }

    /**
     * Show a specific course for the student.
     */
    public function show(Request $request, int $courseId): Response
    {
        $user = Auth::user();
        
        $enrollment = Enrollment::where('user_id', $user->id)
            ->where('course_id', $courseId)
            ->with(['course.lecturer', 'course.semester', 'grade'])
            ->firstOrFail();

        return Inertia::render('student/course-detail', [
            'enrollment' => $enrollment,
        ]);
    }
}
