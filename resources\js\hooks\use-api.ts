import { router } from '@inertiajs/react';
import { useState, useEffect, useCallback } from 'react';
import type { ApiResponse, PaginatedResponse } from '@/types';

interface UseApiOptions {
    immediate?: boolean;
    onSuccess?: (data: any) => void;
    onError?: (error: any) => void;
}

interface UseApiState<T> {
    data: T | null;
    loading: boolean;
    error: string | null;
}

export function useApi<T = any>(
    url: string | null,
    options: UseApiOptions = {}
) {
    const { immediate = true, onSuccess, onError } = options;
    const [state, setState] = useState<UseApiState<T>>({
        data: null,
        loading: false,
        error: null,
    });

    const execute = useCallback(async (customUrl?: string) => {
        const targetUrl = customUrl || url;
        if (!targetUrl) return;

        setState(prev => ({ ...prev, loading: true, error: null }));

        try {
            const response = await fetch(targetUrl, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
                credentials: 'same-origin',
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result: ApiResponse<T> = await response.json();

            if (result.success) {
                setState(prev => ({ ...prev, data: result.data || null, loading: false }));
                onSuccess?.(result.data);
            } else {
                throw new Error(result.message || 'API request failed');
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
            setState(prev => ({ ...prev, error: errorMessage, loading: false }));
            onError?.(err);
        }
    }, [url, onSuccess, onError]);

    useEffect(() => {
        if (immediate && url) {
            execute();
        }
    }, [execute, immediate, url]);

    const refetch = useCallback(() => execute(), [execute]);

    return {
        ...state,
        execute,
        refetch,
    };
}

export function useApiMutation<T = any, P = any>() {
    const [state, setState] = useState<UseApiState<T>>({
        data: null,
        loading: false,
        error: null,
    });

    const mutate = useCallback(async (
        url: string,
        options: {
            method?: 'POST' | 'PUT' | 'PATCH' | 'DELETE';
            data?: P;
            onSuccess?: (data: T) => void;
            onError?: (error: any) => void;
        } = {}
    ) => {
        const { method = 'POST', data, onSuccess, onError } = options;

        setState(prev => ({ ...prev, loading: true, error: null }));

        try {
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            
            const response = await fetch(url, {
                method,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': csrfToken || '',
                },
                credentials: 'same-origin',
                body: data ? JSON.stringify(data) : undefined,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result: ApiResponse<T> = await response.json();

            if (result.success) {
                setState(prev => ({ ...prev, data: result.data || null, loading: false }));
                onSuccess?.(result.data);
                return result.data;
            } else {
                throw new Error(result.message || 'API request failed');
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
            setState(prev => ({ ...prev, error: errorMessage, loading: false }));
            onError?.(err);
            throw err;
        }
    }, []);

    return {
        ...state,
        mutate,
    };
}

// Specific hooks for common API endpoints
export function useDashboardData() {
    return useApi('/api/dashboard');
}

export function useStudentCourses() {
    return useApi('/api/student/my-courses');
}

export function useStudentGrades() {
    return useApi('/api/student/my-grades');
}

export function useStudentAssignments() {
    return useApi('/api/student/my-assignments');
}

export function useStudentPayments() {
    return useApi('/api/student/my-payments');
}

export function useLecturerCourses() {
    return useApi('/api/lecturer/my-courses');
}

export function useLecturerAssignments() {
    return useApi('/api/lecturer/my-assignments');
}

export function usePendingSubmissions() {
    return useApi('/api/lecturer/pending-submissions');
}

// Utility function for Inertia navigation with loading state
export function useInertiaNavigation() {
    const [loading, setLoading] = useState(false);

    const navigate = useCallback((url: string, options: any = {}) => {
        setLoading(true);
        router.visit(url, {
            ...options,
            onFinish: () => {
                setLoading(false);
                options.onFinish?.();
            },
        });
    }, []);

    return { navigate, loading };
}

// Helper for handling form submissions with Inertia
export function useInertiaForm<T extends Record<string, any>>(
    initialData: T,
    submitUrl: string,
    options: {
        method?: 'post' | 'put' | 'patch' | 'delete';
        onSuccess?: () => void;
        onError?: (errors: Record<string, string>) => void;
    } = {}
) {
    const { method = 'post', onSuccess, onError } = options;
    const [data, setData] = useState<T>(initialData);
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [processing, setProcessing] = useState(false);

    const setField = useCallback((field: keyof T, value: any) => {
        setData(prev => ({ ...prev, [field]: value }));
        // Clear error for this field when user starts typing
        if (errors[field as string]) {
            setErrors(prev => ({ ...prev, [field as string]: '' }));
        }
    }, [errors]);

    const submit = useCallback((e?: React.FormEvent) => {
        e?.preventDefault();
        setProcessing(true);
        setErrors({});

        router[method](submitUrl, data, {
            onSuccess: () => {
                setProcessing(false);
                onSuccess?.();
            },
            onError: (errors) => {
                setProcessing(false);
                setErrors(errors);
                onError?.(errors);
            },
        });
    }, [data, method, submitUrl, onSuccess, onError]);

    const reset = useCallback(() => {
        setData(initialData);
        setErrors({});
    }, [initialData]);

    return {
        data,
        setData,
        setField,
        errors,
        processing,
        submit,
        reset,
    };
}
