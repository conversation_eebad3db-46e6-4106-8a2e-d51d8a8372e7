<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Assignment;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\FeeThreshold;
use App\Models\Grade;
use App\Models\Payment;
use App\Models\Semester;
use App\Models\Submission;
use App\Models\User;
use App\Services\GradeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    protected GradeService $gradeService;

    public function __construct(GradeService $gradeService)
    {
        $this->gradeService = $gradeService;
    }

    /**
     * Get role-specific dashboard data.
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();

        if ($user->isStudent()) {
            return $this->getStudentDashboard($user);
        } elseif ($user->isLecturer()) {
            return $this->getLecturerDashboard($user);
        } elseif ($user->isAdmin()) {
            return $this->getAdminDashboard($user);
        } elseif ($user->isAccountant()) {
            return $this->getAccountantDashboard($user);
        } elseif ($user->isSuperAdmin()) {
            return $this->getSuperAdminDashboard($user);
        }

        return response()->json([
            'success' => false,
            'message' => 'Invalid user role.'
        ], 403);
    }

    /**
     * Get student dashboard data.
     */
    private function getStudentDashboard(User $student): JsonResponse
    {
        $currentSemester = Semester::current()->first();

        if (!$currentSemester) {
            return response()->json([
                'success' => false,
                'message' => 'No active semester found.'
            ], 404);
        }

        // Get current enrollments
        $enrollments = Enrollment::where('user_id', $student->id)
            ->where('semester_id', $currentSemester->id)
            ->where('status', 'enrolled')
            ->with(['course.lecturer'])
            ->get();

        // Get pending assignments
        $courseIds = $enrollments->pluck('course_id');
        $pendingAssignments = Assignment::whereIn('course_id', $courseIds)
            ->where('status', 'published')
            ->where('due_date', '>', now())
            ->whereDoesntHave('submissions', function ($query) use ($student) {
                $query->where('user_id', $student->id);
            })
            ->with('course')
            ->orderBy('due_date')
            ->limit(5)
            ->get();

        // Get recent grades (if fee threshold allows)
        $canAccessGrades = $student->canAccessGrades($currentSemester->id);
        $recentGrades = [];
        if ($canAccessGrades) {
            $recentGrades = Grade::whereHas('enrollment', function ($query) use ($student, $currentSemester) {
                $query->where('user_id', $student->id)
                    ->where('semester_id', $currentSemester->id);
            })
                ->with(['enrollment.course'])
                ->orderBy('graded_at', 'desc')
                ->limit(5)
                ->get();
        }

        // Get GPA information
        $sgpa = $canAccessGrades ? $student->getSGPA($currentSemester->id) : null;
        $cgpa = $canAccessGrades ? $student->getCGPA() : null;

        // Get outstanding balance
        $outstandingBalance = $student->getOutstandingBalance($currentSemester->id);
        $feeThreshold = FeeThreshold::getGradeAccessThreshold();

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $student->only(['id', 'name', 'email', 'student_id']),
                'current_semester' => $currentSemester,
                'enrollments' => $enrollments,
                'pending_assignments' => $pendingAssignments,
                'recent_grades' => $recentGrades,
                'gpa' => [
                    'sgpa' => $sgpa,
                    'cgpa' => $cgpa,
                    'can_access' => $canAccessGrades,
                ],
                'financial' => [
                    'outstanding_balance' => $outstandingBalance,
                    'fee_threshold' => $feeThreshold,
                    'can_access_grades' => $canAccessGrades,
                ],
                'stats' => [
                    'total_courses' => $enrollments->count(),
                    'pending_assignments' => $pendingAssignments->count(),
                    'completed_assignments' => Submission::where('user_id', $student->id)->count(),
                ],
            ]
        ]);
    }

    /**
     * Get lecturer dashboard data.
     */
    private function getLecturerDashboard(User $lecturer): JsonResponse
    {
        $currentSemester = Semester::current()->first();

        if (!$currentSemester) {
            return response()->json([
                'success' => false,
                'message' => 'No active semester found.'
            ], 404);
        }

        // Get courses taught by lecturer
        $courses = Course::where('lecturer_id', $lecturer->id)
            ->where('semester_id', $currentSemester->id)
            ->where('is_active', true)
            ->withCount(['enrollments as enrolled_students_count' => function ($query) {
                $query->where('status', 'enrolled');
            }])
            ->get();

        // Get recent assignments
        $recentAssignments = Assignment::whereIn('course_id', $courses->pluck('id'))
            ->with('course')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get pending submissions to grade
        $pendingSubmissions = Submission::whereHas('assignment', function ($query) use ($courses) {
            $query->whereIn('course_id', $courses->pluck('id'));
        })
            ->where('status', 'submitted')
            ->with(['assignment.course', 'student'])
            ->orderBy('submitted_at')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $lecturer->only(['id', 'name', 'email']),
                'current_semester' => $currentSemester,
                'courses' => $courses,
                'recent_assignments' => $recentAssignments,
                'pending_submissions' => $pendingSubmissions,
                'stats' => [
                    'total_courses' => $courses->count(),
                    'total_students' => $courses->sum('enrolled_students_count'),
                    'total_assignments' => Assignment::whereIn('course_id', $courses->pluck('id'))->count(),
                    'pending_submissions' => $pendingSubmissions->count(),
                ],
            ]
        ]);
    }

    /**
     * Get admin dashboard data.
     */
    private function getAdminDashboard(User $admin): JsonResponse
    {
        $currentSemester = Semester::current()->first();

        // Get system statistics
        $totalStudents = User::role('student')->count();
        $totalLecturers = User::role('lecturer')->count();
        $totalCourses = Course::where('is_active', true)->count();
        $activeSemesters = Semester::where('is_active', true)->count();

        // Get recent enrollments
        $recentEnrollments = Enrollment::with(['student', 'course', 'semester'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $admin->only(['id', 'name', 'email']),
                'current_semester' => $currentSemester,
                'recent_enrollments' => $recentEnrollments,
                'stats' => [
                    'total_students' => $totalStudents,
                    'total_lecturers' => $totalLecturers,
                    'total_courses' => $totalCourses,
                    'active_semesters' => $activeSemesters,
                ],
            ]
        ]);
    }

    /**
     * Get accountant dashboard data.
     */
    private function getAccountantDashboard(User $accountant): JsonResponse
    {
        $currentSemester = Semester::current()->first();

        // Get payment statistics
        $totalPendingPayments = Payment::where('status', 'pending')->sum('amount');
        $totalCompletedPayments = Payment::where('status', 'completed')->sum('amount');
        $overduePayments = Payment::where('due_date', '<', now())
            ->where('status', 'pending')
            ->count();

        // Get recent payments
        $recentPayments = Payment::with(['student', 'semester'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get students exceeding fee threshold
        $feeThreshold = FeeThreshold::getGradeAccessThreshold();
        $studentsExceedingThreshold = [];

        if ($feeThreshold && $currentSemester) {
            $students = User::role('student')->get();
            foreach ($students as $student) {
                $balance = $student->getOutstandingBalance($currentSemester->id);
                if ($balance > $feeThreshold) {
                    $studentsExceedingThreshold[] = [
                        'student' => $student->only(['id', 'name', 'email', 'student_id']),
                        'outstanding_balance' => $balance,
                    ];
                }
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $accountant->only(['id', 'name', 'email']),
                'current_semester' => $currentSemester,
                'recent_payments' => $recentPayments,
                'students_exceeding_threshold' => $studentsExceedingThreshold,
                'stats' => [
                    'total_pending_payments' => $totalPendingPayments,
                    'total_completed_payments' => $totalCompletedPayments,
                    'overdue_payments' => $overduePayments,
                    'fee_threshold' => $feeThreshold,
                ],
            ]
        ]);
    }

    /**
     * Get super admin dashboard data.
     */
    private function getSuperAdminDashboard(User $superAdmin): JsonResponse
    {
        // Get comprehensive system statistics
        $userStats = [
            'students' => User::role('student')->count(),
            'lecturers' => User::role('lecturer')->count(),
            'admins' => User::role('admin')->count(),
            'accountants' => User::role('accountant')->count(),
            'super_admins' => User::role('super_admin')->count(),
        ];

        $academicStats = [
            'total_semesters' => Semester::count(),
            'active_semesters' => Semester::where('is_active', true)->count(),
            'total_courses' => Course::count(),
            'active_courses' => Course::where('is_active', true)->count(),
            'total_enrollments' => Enrollment::count(),
            'active_enrollments' => Enrollment::where('status', 'enrolled')->count(),
        ];

        $assignmentStats = [
            'total_assignments' => Assignment::count(),
            'published_assignments' => Assignment::where('status', 'published')->count(),
            'total_submissions' => Submission::count(),
            'graded_submissions' => Submission::where('status', 'graded')->count(),
        ];

        $financialStats = [
            'total_payments' => Payment::sum('amount'),
            'pending_payments' => Payment::where('status', 'pending')->sum('amount'),
            'completed_payments' => Payment::where('status', 'completed')->sum('amount'),
            'active_fee_thresholds' => FeeThreshold::where('is_active', true)->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $superAdmin->only(['id', 'name', 'email']),
                'stats' => [
                    'users' => $userStats,
                    'academic' => $academicStats,
                    'assignments' => $assignmentStats,
                    'financial' => $financialStats,
                ],
            ]
        ]);
    }
}