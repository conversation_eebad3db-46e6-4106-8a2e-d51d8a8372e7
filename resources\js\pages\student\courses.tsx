import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DataTable, type Column } from '@/components/ui/data-table';
import { StatsCard, StatsGrid } from '@/components/ui/stats-card';
import { useStudentCourses } from '@/hooks/use-api';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type Enrollment } from '@/types';
import { Head, router } from '@inertiajs/react';
import { BookOpen, Calendar, Clock, GraduationCap } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'My Courses',
        href: '/student/courses',
    },
];

export default function StudentCourses() {
    const { data, loading, error, refetch } = useStudentCourses();

    if (error) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="My Courses" />
                <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                        <h3 className="text-lg font-medium text-red-600">Error loading courses</h3>
                        <p className="mt-2 text-sm text-gray-600">{error}</p>
                        <Button onClick={refetch} className="mt-4" variant="outline">
                            Retry
                        </Button>
                    </div>
                </div>
            </AppLayout>
        );
    }

    const enrollments = data?.enrollments || [];
    const stats = data?.stats || {};

    // Define columns for courses table
    const courseColumns: Column<Enrollment>[] = [
        {
            key: 'course.code',
            title: 'Course Code',
            width: '120px',
            render: (value) => (
                <span className="font-mono font-medium">{value}</span>
            ),
        },
        {
            key: 'course.name',
            title: 'Course Name',
            render: (value, record) => (
                <div>
                    <div className="font-medium">{value}</div>
                    {record.course?.description && (
                        <div className="text-sm text-muted-foreground line-clamp-2">
                            {record.course.description}
                        </div>
                    )}
                </div>
            ),
        },
        {
            key: 'course.credit_hours',
            title: 'Credits',
            width: '80px',
            render: (value) => (
                <span className="font-medium">{value}</span>
            ),
        },
        {
            key: 'course.lecturer.name',
            title: 'Lecturer',
            render: (value) => value || 'TBA',
        },
        {
            key: 'status',
            title: 'Status',
            width: '120px',
            render: (value) => (
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    value === 'enrolled' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : value === 'completed'
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        : value === 'dropped'
                        ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                }`}>
                    {value}
                </span>
            ),
        },
        {
            key: 'enrolled_at',
            title: 'Enrolled',
            width: '120px',
            render: (value) => new Date(value).toLocaleDateString(),
        },
        {
            key: 'grade',
            title: 'Grade',
            width: '80px',
            render: (value) => {
                if (!value) return <span className="text-muted-foreground">-</span>;
                return (
                    <div className="text-center">
                        <div className="font-medium">{value.letter_grade}</div>
                        <div className="text-xs text-muted-foreground">
                            {value.grade_point?.toFixed(2)}
                        </div>
                    </div>
                );
            },
        },
        {
            key: 'actions',
            title: 'Actions',
            width: '120px',
            render: (_, record) => (
                <div className="flex space-x-1">
                    <Button
                        size="sm"
                        variant="outline"
                        onClick={() => router.visit(`/student/courses/${record.course?.id}`)}
                    >
                        View
                    </Button>
                    {record.status === 'enrolled' && (
                        <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => router.visit(`/student/courses/${record.course?.id}/assignments`)}
                        >
                            Assignments
                        </Button>
                    )}
                </div>
            ),
        },
    ];

    // Calculate stats
    const totalCredits = enrollments
        .filter(e => e.status === 'enrolled')
        .reduce((sum, e) => sum + (e.course?.credit_hours || 0), 0);

    const completedCourses = enrollments.filter(e => e.status === 'completed').length;
    const activeCourses = enrollments.filter(e => e.status === 'enrolled').length;
    const droppedCourses = enrollments.filter(e => e.status === 'dropped').length;

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="My Courses" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">My Courses</h1>
                        <p className="text-muted-foreground">
                            View and manage your course enrollments
                        </p>
                    </div>
                    <Button onClick={() => router.visit('/student/enrollment')}>
                        Enroll in Course
                    </Button>
                </div>

                {/* Stats Overview */}
                <StatsGrid>
                    <StatsCard
                        title="Active Courses"
                        value={activeCourses}
                        description="Currently enrolled"
                        icon={BookOpen}
                        loading={loading}
                    />
                    <StatsCard
                        title="Total Credits"
                        value={totalCredits}
                        description="Current semester"
                        icon={GraduationCap}
                        loading={loading}
                    />
                    <StatsCard
                        title="Completed Courses"
                        value={completedCourses}
                        description="All time"
                        icon={Calendar}
                        loading={loading}
                    />
                    <StatsCard
                        title="Dropped Courses"
                        value={droppedCourses}
                        description="All time"
                        icon={Clock}
                        loading={loading}
                    />
                </StatsGrid>

                {/* Course Enrollments */}
                <Card>
                    <CardHeader>
                        <CardTitle>Course Enrollments</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <DataTable
                            data={enrollments}
                            columns={courseColumns}
                            loading={loading}
                            searchable={true}
                            searchPlaceholder="Search courses..."
                            emptyText="No course enrollments found"
                        />
                    </CardContent>
                </Card>

                {/* Course Status Summary */}
                <div className="grid gap-6 md:grid-cols-3">
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-green-600">Active Enrollments</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                {enrollments
                                    .filter(e => e.status === 'enrolled')
                                    .slice(0, 5)
                                    .map((enrollment, index) => (
                                        <div key={index} className="flex justify-between items-center">
                                            <span className="text-sm">{enrollment.course?.code}</span>
                                            <span className="text-xs text-muted-foreground">
                                                {enrollment.course?.credit_hours} credits
                                            </span>
                                        </div>
                                    ))}
                                {enrollments.filter(e => e.status === 'enrolled').length === 0 && (
                                    <p className="text-sm text-muted-foreground">No active enrollments</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-blue-600">Completed Courses</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                {enrollments
                                    .filter(e => e.status === 'completed')
                                    .slice(0, 5)
                                    .map((enrollment, index) => (
                                        <div key={index} className="flex justify-between items-center">
                                            <span className="text-sm">{enrollment.course?.code}</span>
                                            <span className="text-xs font-medium">
                                                {enrollment.grade?.letter_grade || 'N/A'}
                                            </span>
                                        </div>
                                    ))}
                                {enrollments.filter(e => e.status === 'completed').length === 0 && (
                                    <p className="text-sm text-muted-foreground">No completed courses</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-red-600">Dropped Courses</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                {enrollments
                                    .filter(e => e.status === 'dropped')
                                    .slice(0, 5)
                                    .map((enrollment, index) => (
                                        <div key={index} className="flex justify-between items-center">
                                            <span className="text-sm">{enrollment.course?.code}</span>
                                            <span className="text-xs text-muted-foreground">
                                                {enrollment.dropped_at ? 
                                                    new Date(enrollment.dropped_at).toLocaleDateString() : 
                                                    'N/A'
                                                }
                                            </span>
                                        </div>
                                    ))}
                                {enrollments.filter(e => e.status === 'dropped').length === 0 && (
                                    <p className="text-sm text-muted-foreground">No dropped courses</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
