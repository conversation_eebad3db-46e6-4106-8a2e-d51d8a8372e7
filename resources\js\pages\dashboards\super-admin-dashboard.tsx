import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { StatsCard, StatsGrid } from '@/components/ui/stats-card';
import { router } from '@inertiajs/react';
import { Database, Settings, Shield, Users } from 'lucide-react';

interface SystemStats {
    total_users: number;
    total_students: number;
    total_lecturers: number;
    total_admins: number;
    total_accountants: number;
    total_courses: number;
    total_enrollments: number;
    total_payments: number;
    system_health: 'good' | 'warning' | 'critical';
}

interface SuperAdminDashboardProps {
    data: SystemStats;
}

export function SuperAdminDashboard({ data }: SuperAdminDashboardProps) {
    const getHealthColor = (health: string) => {
        switch (health) {
            case 'good':
                return 'text-green-600 dark:text-green-400';
            case 'warning':
                return 'text-yellow-600 dark:text-yellow-400';
            case 'critical':
                return 'text-red-600 dark:text-red-400';
            default:
                return 'text-gray-600 dark:text-gray-400';
        }
    };

    return (
        <div className="space-y-6">
            {/* System Overview */}
            <StatsGrid>
                <StatsCard title="Total Users" value={data?.total_users || 0} description="All system users" icon={Users} />
                <StatsCard title="Total Courses" value={data?.total_courses || 0} description="All courses" icon={Database} />
                <StatsCard title="Total Enrollments" value={data?.total_enrollments || 0} description="All enrollments" icon={Users} />
                <StatsCard
                    title="System Health"
                    value={data?.system_health || 'unknown'}
                    description="Overall status"
                    icon={Shield}
                    className={data?.system_health ? getHealthColor(data.system_health) : ''}
                />
            </StatsGrid>

            {/* User Role Breakdown */}
            <Card>
                <CardHeader>
                    <CardTitle>User Role Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <div className="rounded-lg border p-4 text-center">
                            <div className="text-2xl font-bold text-blue-600">{data?.total_students || 0}</div>
                            <div className="text-sm text-muted-foreground">Students</div>
                        </div>
                        <div className="rounded-lg border p-4 text-center">
                            <div className="text-2xl font-bold text-green-600">{data?.total_lecturers || 0}</div>
                            <div className="text-sm text-muted-foreground">Lecturers</div>
                        </div>
                        <div className="rounded-lg border p-4 text-center">
                            <div className="text-2xl font-bold text-purple-600">{data?.total_admins || 0}</div>
                            <div className="text-sm text-muted-foreground">Admins</div>
                        </div>
                        <div className="rounded-lg border p-4 text-center">
                            <div className="text-2xl font-bold text-orange-600">{data?.total_accountants || 0}</div>
                            <div className="text-sm text-muted-foreground">Accountants</div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* System Administration */}
            <Card>
                <CardHeader>
                    <CardTitle>System Administration</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <Button className="h-24 flex-col" variant="outline" onClick={() => router.visit('/super-admin/users')}>
                            <Users className="mb-2 h-8 w-8" />
                            <span>User Management</span>
                            <span className="text-xs text-muted-foreground">Manage all users and roles</span>
                        </Button>
                        <Button className="h-24 flex-col" variant="outline" onClick={() => router.visit('/super-admin/roles')}>
                            <Shield className="mb-2 h-8 w-8" />
                            <span>Role Management</span>
                            <span className="text-xs text-muted-foreground">Assign and manage roles</span>
                        </Button>
                        <Button className="h-24 flex-col" variant="outline" onClick={() => router.visit('/super-admin/system')}>
                            <Settings className="mb-2 h-8 w-8" />
                            <span>System Settings</span>
                            <span className="text-xs text-muted-foreground">Configure system parameters</span>
                        </Button>
                        <Button className="h-24 flex-col" variant="outline" onClick={() => router.visit('/super-admin/database')}>
                            <Database className="mb-2 h-8 w-8" />
                            <span>Database Management</span>
                            <span className="text-xs text-muted-foreground">Backup and maintenance</span>
                        </Button>
                        <Button className="h-24 flex-col" variant="outline" onClick={() => router.visit('/super-admin/logs')}>
                            <Shield className="mb-2 h-8 w-8" />
                            <span>System Logs</span>
                            <span className="text-xs text-muted-foreground">View system activity</span>
                        </Button>
                        <Button className="h-24 flex-col" variant="outline" onClick={() => router.visit('/super-admin/reports')}>
                            <Database className="mb-2 h-8 w-8" />
                            <span>System Reports</span>
                            <span className="text-xs text-muted-foreground">Generate system reports</span>
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
                <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Button onClick={() => router.visit('/super-admin/users/create')}>Create User</Button>
                        <Button variant="outline" onClick={() => router.visit('/super-admin/backup')}>
                            Backup System
                        </Button>
                        <Button variant="outline" onClick={() => router.visit('/super-admin/maintenance')}>
                            Maintenance Mode
                        </Button>
                        <Button variant="outline" onClick={() => router.visit('/super-admin/cache')}>
                            Clear Cache
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* System Health Details */}
            <Card>
                <CardHeader>
                    <CardTitle>System Health Details</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        <div className="flex items-center justify-between rounded-lg border p-3">
                            <span>Database Connection</span>
                            <span className="font-medium text-green-600">✓ Connected</span>
                        </div>
                        <div className="flex items-center justify-between rounded-lg border p-3">
                            <span>Cache System</span>
                            <span className="font-medium text-green-600">✓ Active</span>
                        </div>
                        <div className="flex items-center justify-between rounded-lg border p-3">
                            <span>File Storage</span>
                            <span className="font-medium text-green-600">✓ Available</span>
                        </div>
                        <div className="flex items-center justify-between rounded-lg border p-3">
                            <span>Email Service</span>
                            <span className="font-medium text-green-600">✓ Operational</span>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
