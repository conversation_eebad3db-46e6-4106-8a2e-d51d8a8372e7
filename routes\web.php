<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
});

// Test route to verify our academic system
Route::get('/test-system', function () {
    $data = [
        'users_count' => \App\Models\User::count(),
        'students_count' => \App\Models\User::role('student')->count(),
        'lecturers_count' => \App\Models\User::role('lecturer')->count(),
        'semesters_count' => \App\Models\Semester::count(),
        'courses_count' => \App\Models\Course::count(),
        'enrollments_count' => \App\Models\Enrollment::count(),
        'grades_count' => \App\Models\Grade::count(),
        'payments_count' => \App\Models\Payment::count(),
        'fee_thresholds_count' => \App\Models\FeeThreshold::count(),
        'current_semester' => \App\Models\Semester::current()->first(),
        'sample_student' => \App\Models\User::role('student')->first(),
    ];

    return response()->json([
        'success' => true,
        'message' => 'Academic system is working!',
        'data' => $data
    ]);
});

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
