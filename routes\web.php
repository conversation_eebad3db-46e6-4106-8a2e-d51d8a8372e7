<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [\App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');

    // Student routes
    Route::prefix('student')->middleware('role:student')->group(function () {
        Route::get('courses', [\App\Http\Controllers\Student\CourseController::class, 'index'])->name('student.courses');
        Route::get('courses/{course}', [\App\Http\Controllers\Student\CourseController::class, 'show'])->name('student.courses.show');

        Route::get('grades', function () {
            return Inertia::render('student/grades');
        })->name('student.grades');

        Route::get('assignments', function () {
            return Inertia::render('student/assignments');
        })->name('student.assignments');

        Route::get('payments', function () {
            return Inertia::render('student/payments');
        })->name('student.payments');
    });

    // Lecturer routes
    Route::prefix('lecturer')->middleware('role:lecturer')->group(function () {
        Route::get('courses', function () {
            return Inertia::render('lecturer/courses');
        })->name('lecturer.courses');

        Route::get('assignments', function () {
            return Inertia::render('lecturer/assignments');
        })->name('lecturer.assignments');

        Route::get('submissions', function () {
            return Inertia::render('lecturer/submissions');
        })->name('lecturer.submissions');

        Route::get('grades', function () {
            return Inertia::render('lecturer/grades');
        })->name('lecturer.grades');
    });

    // Admin routes
    Route::prefix('admin')->middleware('role:admin')->group(function () {
        Route::get('users', function () {
            return Inertia::render('admin/users');
        })->name('admin.users');

        Route::get('courses', function () {
            return Inertia::render('admin/courses');
        })->name('admin.courses');

        Route::get('semesters', function () {
            return Inertia::render('admin/semesters');
        })->name('admin.semesters');

        Route::get('enrollments', function () {
            return Inertia::render('admin/enrollments');
        })->name('admin.enrollments');

        Route::get('reports', function () {
            return Inertia::render('admin/reports');
        })->name('admin.reports');
    });

    // Accountant routes
    Route::prefix('accountant')->middleware('role:accountant')->group(function () {
        Route::get('payments', function () {
            return Inertia::render('accountant/payments');
        })->name('accountant.payments');

        Route::get('thresholds', function () {
            return Inertia::render('accountant/thresholds');
        })->name('accountant.thresholds');

        Route::get('reports', function () {
            return Inertia::render('accountant/reports');
        })->name('accountant.reports');

        Route::get('overdue', function () {
            return Inertia::render('accountant/overdue');
        })->name('accountant.overdue');
    });

    // Super Admin routes
    Route::prefix('super-admin')->middleware('role:super_admin')->group(function () {
        Route::get('system', function () {
            return Inertia::render('super-admin/system');
        })->name('super-admin.system');

        Route::get('users', function () {
            return Inertia::render('super-admin/users');
        })->name('super-admin.users');

        Route::get('database', function () {
            return Inertia::render('super-admin/database');
        })->name('super-admin.database');

        Route::get('reports', function () {
            return Inertia::render('super-admin/reports');
        })->name('super-admin.reports');
    });
});

// Test route to verify our academic system
Route::get('/test-system', function () {
    $data = [
        'users_count' => \App\Models\User::count(),
        'students_count' => \App\Models\User::role('student')->count(),
        'lecturers_count' => \App\Models\User::role('lecturer')->count(),
        'semesters_count' => \App\Models\Semester::count(),
        'courses_count' => \App\Models\Course::count(),
        'enrollments_count' => \App\Models\Enrollment::count(),
        'grades_count' => \App\Models\Grade::count(),
        'payments_count' => \App\Models\Payment::count(),
        'fee_thresholds_count' => \App\Models\FeeThreshold::count(),
        'current_semester' => \App\Models\Semester::current()->first(),
        'sample_student' => \App\Models\User::role('student')->first(),
    ];

    return response()->json([
        'success' => true,
        'message' => 'Academic system is working!',
        'data' => $data
    ]);
});

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
