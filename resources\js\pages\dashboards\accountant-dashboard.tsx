import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DataTable, type Column } from '@/components/ui/data-table';
import { StatsCard, StatsGrid } from '@/components/ui/stats-card';
import { type AccountantDashboardData, type Payment } from '@/types';
import { router } from '@inertiajs/react';
import { AlertTriangle, DollarSign, TrendingDown, TrendingUp } from 'lucide-react';

interface AccountantDashboardProps {
    data: AccountantDashboardData;
}

export function AccountantDashboard({ data }: AccountantDashboardProps) {
    // Define columns for recent payments table
    const paymentColumns: Column<Payment>[] = [
        {
            key: 'user.name',
            title: 'Student',
        },
        {
            key: 'user.student_id',
            title: 'Student ID',
            width: '120px',
        },
        {
            key: 'amount',
            title: 'Amount',
            width: '120px',
            render: (value) => `$${value.toFixed(2)}`,
        },
        {
            key: 'payment_type',
            title: 'Type',
            width: '100px',
            render: (value) => <span className="capitalize">{value.replace('_', ' ')}</span>,
        },
        {
            key: 'payment_method',
            title: 'Method',
            width: '120px',
            render: (value) => <span className="capitalize">{value.replace('_', ' ')}</span>,
        },
        {
            key: 'status',
            title: 'Status',
            width: '100px',
            render: (value) => (
                <span
                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                        value === 'completed'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : value === 'pending'
                              ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                              : value === 'failed'
                                ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                    }`}
                >
                    {value}
                </span>
            ),
        },
        {
            key: 'paid_at',
            title: 'Date',
            width: '120px',
            render: (value) => (value ? new Date(value).toLocaleDateString() : 'N/A'),
        },
    ];

    // Define columns for overdue payments table
    const overdueColumns: Column<Payment>[] = [
        {
            key: 'user.name',
            title: 'Student',
        },
        {
            key: 'user.student_id',
            title: 'Student ID',
            width: '120px',
        },
        {
            key: 'amount',
            title: 'Amount',
            width: '120px',
            render: (value) => `$${value.toFixed(2)}`,
        },
        {
            key: 'payment_type',
            title: 'Type',
            width: '100px',
            render: (value) => <span className="capitalize">{value.replace('_', ' ')}</span>,
        },
        {
            key: 'created_at',
            title: 'Due Since',
            width: '120px',
            render: (value) => {
                const daysPast = Math.floor((new Date().getTime() - new Date(value).getTime()) / (1000 * 60 * 60 * 24));
                return `${daysPast} days`;
            },
        },
        {
            key: 'actions',
            title: 'Actions',
            width: '120px',
            render: (_, record) => (
                <Button size="sm" variant="outline" onClick={() => router.visit(`/accountant/payments/${record.id}`)}>
                    Follow Up
                </Button>
            ),
        },
    ];

    return (
        <div className="space-y-6">
            {/* Financial Stats Overview */}
            <StatsGrid>
                <StatsCard
                    title="Total Payments"
                    value={data?.financial_stats?.total_payments ? `$${data.financial_stats.total_payments.toFixed(2)}` : '$0.00'}
                    description="Current semester"
                    icon={DollarSign}
                />
                <StatsCard
                    title="Outstanding Balance"
                    value={data?.financial_stats?.outstanding_balance ? `$${data.financial_stats.outstanding_balance.toFixed(2)}` : '$0.00'}
                    description="Total unpaid"
                    icon={TrendingDown}
                />
                <StatsCard
                    title="Students Over Threshold"
                    value={data?.financial_stats?.students_exceeding_threshold || 0}
                    description="Grade access restricted"
                    icon={AlertTriangle}
                />
                <StatsCard
                    title="Payment Rate"
                    value={data?.financial_stats?.payment_completion_rate ? `${data.financial_stats.payment_completion_rate.toFixed(1)}%` : '0%'}
                    description="Completion rate"
                    icon={TrendingUp}
                />
            </StatsGrid>

            {/* Quick Actions */}
            <Card>
                <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Button className="h-20 flex-col" variant="outline" onClick={() => router.visit('/accountant/payments')}>
                            <DollarSign className="mb-2 h-6 w-6" />
                            Manage Payments
                        </Button>
                        <Button className="h-20 flex-col" variant="outline" onClick={() => router.visit('/accountant/reports')}>
                            <TrendingUp className="mb-2 h-6 w-6" />
                            Financial Reports
                        </Button>
                        <Button className="h-20 flex-col" variant="outline" onClick={() => router.visit('/accountant/thresholds')}>
                            <AlertTriangle className="mb-2 h-6 w-6" />
                            Fee Thresholds
                        </Button>
                        <Button className="h-20 flex-col" variant="outline" onClick={() => router.visit('/accountant/overdue')}>
                            <TrendingDown className="mb-2 h-6 w-6" />
                            Overdue Payments
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Overdue Payments */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle className="flex items-center">
                        <AlertTriangle className="mr-2 h-5 w-5 text-red-500" />
                        Overdue Payments
                    </CardTitle>
                    <Button variant="outline" size="sm" onClick={() => router.visit('/accountant/overdue')}>
                        View All
                    </Button>
                </CardHeader>
                <CardContent>
                    <DataTable
                        data={data?.overdue_payments || []}
                        columns={overdueColumns}
                        searchable={true}
                        searchPlaceholder="Search overdue payments..."
                        emptyText="No overdue payments"
                    />
                </CardContent>
            </Card>

            {/* Recent Payments */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Recent Payments</CardTitle>
                    <Button variant="outline" size="sm" onClick={() => router.visit('/accountant/payments')}>
                        View All
                    </Button>
                </CardHeader>
                <CardContent>
                    <DataTable
                        data={data?.recent_payments || []}
                        columns={paymentColumns}
                        searchable={true}
                        searchPlaceholder="Search payments..."
                        emptyText="No recent payments"
                    />
                </CardContent>
            </Card>

            {/* Current Semester Info */}
            {data?.current_semester && (
                <Card>
                    <CardHeader>
                        <CardTitle>Current Semester</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2">
                            <div>
                                <h4 className="font-medium">{data.current_semester.name}</h4>
                                <p className="text-sm text-muted-foreground">{data.current_semester.code}</p>
                            </div>
                            <div>
                                <p className="text-sm">
                                    <span className="font-medium">Period:</span> {new Date(data.current_semester.start_date).toLocaleDateString()} -{' '}
                                    {new Date(data.current_semester.end_date).toLocaleDateString()}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
