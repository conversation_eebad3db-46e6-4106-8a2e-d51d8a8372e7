import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem, type User } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import {
    AlertTriangle,
    BookOpen,
    Calendar,
    Database,
    DollarSign,
    FileText,
    Folder,
    GraduationCap,
    LayoutGrid,
    Settings,
    Shield,
    TrendingUp,
    Users,
} from 'lucide-react';
import AppLogo from './app-logo';

// Role-based navigation items
const getNavItemsForRole = (role: string): NavItem[] => {
    const baseItems: NavItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
            icon: LayoutGrid,
        },
    ];

    switch (role) {
        case 'student':
            return [
                ...baseItems,
                {
                    title: 'My Courses',
                    href: '/student/courses',
                    icon: BookOpen,
                },
                {
                    title: 'My Grades',
                    href: '/student/grades',
                    icon: GraduationCap,
                },
                {
                    title: 'Assignments',
                    href: '/student/assignments',
                    icon: FileText,
                },
                {
                    title: 'Payments',
                    href: '/student/payments',
                    icon: DollarSign,
                },
            ];

        case 'lecturer':
            return [
                ...baseItems,
                {
                    title: 'My Courses',
                    href: '/lecturer/courses',
                    icon: BookOpen,
                },
                {
                    title: 'Assignments',
                    href: '/lecturer/assignments',
                    icon: FileText,
                },
                {
                    title: 'Submissions',
                    href: '/lecturer/submissions',
                    icon: GraduationCap,
                },
                {
                    title: 'Grade Book',
                    href: '/lecturer/grades',
                    icon: TrendingUp,
                },
            ];

        case 'admin':
            return [
                ...baseItems,
                {
                    title: 'User Management',
                    href: '/admin/users',
                    icon: Users,
                },
                {
                    title: 'Course Management',
                    href: '/admin/courses',
                    icon: BookOpen,
                },
                {
                    title: 'Semester Management',
                    href: '/admin/semesters',
                    icon: Calendar,
                },
                {
                    title: 'Enrollments',
                    href: '/admin/enrollments',
                    icon: GraduationCap,
                },
                {
                    title: 'Reports',
                    href: '/admin/reports',
                    icon: TrendingUp,
                },
            ];

        case 'accountant':
            return [
                ...baseItems,
                {
                    title: 'Payment Management',
                    href: '/accountant/payments',
                    icon: DollarSign,
                },
                {
                    title: 'Fee Thresholds',
                    href: '/accountant/thresholds',
                    icon: AlertTriangle,
                },
                {
                    title: 'Financial Reports',
                    href: '/accountant/reports',
                    icon: TrendingUp,
                },
                {
                    title: 'Overdue Payments',
                    href: '/accountant/overdue',
                    icon: AlertTriangle,
                },
            ];

        case 'super_admin':
            return [
                ...baseItems,
                {
                    title: 'System Management',
                    href: '/super-admin/system',
                    icon: Settings,
                },
                {
                    title: 'User & Roles',
                    href: '/super-admin/users',
                    icon: Shield,
                },
                {
                    title: 'Database',
                    href: '/super-admin/database',
                    icon: Database,
                },
                {
                    title: 'System Reports',
                    href: '/super-admin/reports',
                    icon: TrendingUp,
                },
            ];

        default:
            return baseItems;
    }
};

const footerNavItems: NavItem[] = [
    {
        title: 'Repository',
        href: 'https://github.com/laravel/react-starter-kit',
        icon: Folder,
    },
    {
        title: 'Documentation',
        href: 'https://laravel.com/docs/starter-kits#react',
        icon: BookOpen,
    },
];

export function AppSidebar() {
    const { auth } = usePage().props as { auth: { user: User } };
    const user = auth.user;

    // Get user's primary role
    const primaryRole = user.roles?.[0]?.name || user.primary_role || 'student';

    // Get navigation items based on user role
    const navItems = getNavItemsForRole(primaryRole);

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={navItems} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
