<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class BulkGradeImportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check() && Auth::user()->can('can:bulk-import-grades');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'grades' => 'required|array|min:1',
            'grades.*.student_id' => 'required|integer|exists:users,id',
            'grades.*.course_id' => 'required|integer|exists:courses,id',
            'grades.*.grade_point' => 'nullable|numeric|between:0,4.00',
            'grades.*.letter_grade' => 'nullable|string|max:2',
            'grades.*.percentage' => 'nullable|numeric|between:0,100',
            'grades.*.comments' => 'nullable|string|max:1000',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'grades.required' => 'Grades data is required.',
            'grades.array' => 'Grades must be an array.',
            'grades.min' => 'At least one grade entry is required.',
            'grades.*.student_id.required' => 'Student ID is required for each grade entry.',
            'grades.*.student_id.exists' => 'One or more student IDs do not exist.',
            'grades.*.course_id.required' => 'Course ID is required for each grade entry.',
            'grades.*.course_id.exists' => 'One or more course IDs do not exist.',
            'grades.*.grade_point.between' => 'Grade point must be between 0 and 4.00.',
            'grades.*.percentage.between' => 'Percentage must be between 0 and 100.',
            'grades.*.letter_grade.max' => 'Letter grade cannot exceed 2 characters.',
            'grades.*.comments.max' => 'Comments cannot exceed 1000 characters.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            foreach ($this->grades as $index => $grade) {
                // Ensure at least one grading method is provided for each grade
                if (!isset($grade['grade_point']) && !isset($grade['percentage']) && !isset($grade['letter_grade'])) {
                    $validator->errors()->add("grades.{$index}.grade", 'At least one grading method must be provided for each grade entry.');
                }
            }
        });
    }
}
