import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DataTable, type Column } from '@/components/ui/data-table';
import { StatsCard, StatsGrid } from '@/components/ui/stats-card';
import { useApi } from '@/hooks/use-api';
import { type AdminDashboardData, type Course, type Enrollment } from '@/types';
import { router } from '@inertiajs/react';
import { BookOpen, Calendar, GraduationCap, UserCheck, Users } from 'lucide-react';

export function AdminDashboard() {
    const { data, loading, error } = useApi<AdminDashboardData>('/api/admin/dashboard');

    if (error) {
        return (
            <div className="flex h-64 items-center justify-center">
                <div className="text-center">
                    <h3 className="text-lg font-medium text-red-600">Error loading dashboard</h3>
                    <p className="mt-2 text-sm text-gray-600">{error}</p>
                    <Button onClick={() => window.location.reload()} className="mt-4" variant="outline">
                        Retry
                    </Button>
                </div>
            </div>
        );
    }

    // Define columns for recent enrollments table
    const enrollmentColumns: Column<Enrollment>[] = [
        {
            key: 'user.name',
            title: 'Student',
        },
        {
            key: 'user.student_id',
            title: 'Student ID',
            width: '120px',
        },
        {
            key: 'course.code',
            title: 'Course Code',
            width: '120px',
        },
        {
            key: 'course.name',
            title: 'Course Name',
        },
        {
            key: 'enrolled_at',
            title: 'Enrolled',
            width: '120px',
            render: (value) => new Date(value).toLocaleDateString(),
        },
        {
            key: 'status',
            title: 'Status',
            width: '100px',
            render: (value) => (
                <span
                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                        value === 'enrolled'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : value === 'completed'
                              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                    }`}
                >
                    {value}
                </span>
            ),
        },
    ];

    // Define columns for course statistics table
    const courseStatsColumns: Column<{ course: Course; enrollment_count: number }>[] = [
        {
            key: 'course.code',
            title: 'Course Code',
            width: '120px',
        },
        {
            key: 'course.name',
            title: 'Course Name',
        },
        {
            key: 'course.lecturer.name',
            title: 'Lecturer',
            render: (value) => value || 'TBA',
        },
        {
            key: 'course.credit_hours',
            title: 'Credits',
            width: '80px',
        },
        {
            key: 'enrollment_count',
            title: 'Enrolled',
            width: '100px',
            render: (value) => <span className="font-medium">{value}</span>,
        },
        {
            key: 'actions',
            title: 'Actions',
            width: '120px',
            render: (_, record) => (
                <Button size="sm" variant="outline" onClick={() => router.visit(`/admin/courses/${record.course.id}`)}>
                    Manage
                </Button>
            ),
        },
    ];

    return (
        <div className="space-y-6">
            {/* Stats Overview */}
            <StatsGrid>
                <StatsCard
                    title="Total Students"
                    value={data?.stats?.total_students || 0}
                    description="Active students"
                    icon={GraduationCap}
                    loading={loading}
                />
                <StatsCard
                    title="Total Lecturers"
                    value={data?.stats?.total_lecturers || 0}
                    description="Active lecturers"
                    icon={UserCheck}
                    loading={loading}
                />
                <StatsCard
                    title="Total Courses"
                    value={data?.stats?.total_courses || 0}
                    description="Current semester"
                    icon={BookOpen}
                    loading={loading}
                />
                <StatsCard
                    title="Total Enrollments"
                    value={data?.stats?.total_enrollments || 0}
                    description="Current semester"
                    icon={Users}
                    loading={loading}
                />
            </StatsGrid>

            {/* Quick Actions */}
            <Card>
                <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Button className="h-20 flex-col" variant="outline" onClick={() => router.visit('/admin/users')}>
                            <Users className="mb-2 h-6 w-6" />
                            Manage Users
                        </Button>
                        <Button className="h-20 flex-col" variant="outline" onClick={() => router.visit('/admin/courses')}>
                            <BookOpen className="mb-2 h-6 w-6" />
                            Manage Courses
                        </Button>
                        <Button className="h-20 flex-col" variant="outline" onClick={() => router.visit('/admin/semesters')}>
                            <Calendar className="mb-2 h-6 w-6" />
                            Manage Semesters
                        </Button>
                        <Button className="h-20 flex-col" variant="outline" onClick={() => router.visit('/admin/enrollments')}>
                            <UserCheck className="mb-2 h-6 w-6" />
                            Manage Enrollments
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Course Statistics */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Course Statistics</CardTitle>
                    <Button variant="outline" size="sm" onClick={() => router.visit('/admin/reports/courses')}>
                        View Reports
                    </Button>
                </CardHeader>
                <CardContent>
                    <DataTable
                        data={data?.course_statistics || []}
                        columns={courseStatsColumns}
                        loading={loading}
                        searchable={true}
                        searchPlaceholder="Search courses..."
                        emptyText="No course data available"
                    />
                </CardContent>
            </Card>

            {/* Recent Enrollments */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Recent Enrollments</CardTitle>
                    <Button variant="outline" size="sm" onClick={() => router.visit('/admin/enrollments')}>
                        View All
                    </Button>
                </CardHeader>
                <CardContent>
                    <DataTable
                        data={data?.recent_enrollments || []}
                        columns={enrollmentColumns}
                        loading={loading}
                        searchable={true}
                        searchPlaceholder="Search enrollments..."
                        emptyText="No recent enrollments"
                    />
                </CardContent>
            </Card>

            {/* Current Semester Info */}
            {data?.current_semester && (
                <Card>
                    <CardHeader>
                        <CardTitle>Current Semester</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2">
                            <div>
                                <h4 className="font-medium">{data.current_semester.name}</h4>
                                <p className="text-sm text-muted-foreground">{data.current_semester.code}</p>
                                {data.current_semester.description && (
                                    <p className="mt-1 text-sm text-muted-foreground">{data.current_semester.description}</p>
                                )}
                            </div>
                            <div>
                                <p className="text-sm">
                                    <span className="font-medium">Start Date:</span> {new Date(data.current_semester.start_date).toLocaleDateString()}
                                </p>
                                <p className="text-sm">
                                    <span className="font-medium">End Date:</span> {new Date(data.current_semester.end_date).toLocaleDateString()}
                                </p>
                                <p className="text-sm">
                                    <span className="font-medium">Status:</span>{' '}
                                    <span
                                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                                            data.current_semester.is_active
                                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                                : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                                        }`}
                                    >
                                        {data.current_semester.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
