<?php

namespace App\Console\Commands;

use App\Services\NotificationService;
use Illuminate\Console\Command;

class SendAutomatedNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:send {--type=all : Type of notifications to send (assignment|payment|all)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send automated notifications for assignments, payments, and other alerts';

    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');

        $this->info('Starting automated notification sending...');

        $results = [];

        if ($type === 'assignment' || $type === 'all') {
            $this->info('Sending assignment deadline reminders...');
            $result = $this->notificationService->sendAssignmentDeadlineReminders();
            $results['assignment'] = $result;
            $this->line($result['message']);
        }

        if ($type === 'payment' || $type === 'all') {
            $this->info('Sending payment reminders...');
            $result = $this->notificationService->sendPaymentReminders();
            $results['payment'] = $result;
            $this->line($result['message']);
        }

        // Display summary
        $totalSent = collect($results)->sum('sent');
        $totalErrors = collect($results)->sum(fn($r) => count($r['errors']));

        if ($totalSent > 0) {
            $this->info("✅ Successfully sent {$totalSent} notifications.");
        }

        if ($totalErrors > 0) {
            $this->warn("⚠️  {$totalErrors} notifications failed to send.");

            foreach ($results as $notificationType => $result) {
                if (!empty($result['errors'])) {
                    $this->error("Errors in {$notificationType} notifications:");
                    foreach ($result['errors'] as $error) {
                        $this->line("  - {$error}");
                    }
                }
            }
        }

        if ($totalSent === 0 && $totalErrors === 0) {
            $this->info('No notifications to send at this time.');
        }

        return Command::SUCCESS;
    }
}
