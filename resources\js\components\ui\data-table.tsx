import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { ChevronLeft, ChevronRight, Search } from 'lucide-react';
import { useState, useMemo, type ReactNode } from 'react';

export interface Column<T> {
    key: keyof T | string;
    title: string;
    render?: (value: any, record: T, index: number) => ReactNode;
    sortable?: boolean;
    searchable?: boolean;
    width?: string;
    className?: string;
}

interface DataTableProps<T> {
    data: T[];
    columns: Column<T>[];
    loading?: boolean;
    pagination?: {
        current: number;
        pageSize: number;
        total: number;
        onChange: (page: number, pageSize: number) => void;
    };
    searchable?: boolean;
    searchPlaceholder?: string;
    emptyText?: string;
    className?: string;
}

export function DataTable<T extends Record<string, any>>({
    data,
    columns,
    loading = false,
    pagination,
    searchable = true,
    searchPlaceholder = 'Search...',
    emptyText = 'No data available',
    className,
}: DataTableProps<T>) {
    const [searchTerm, setSearchTerm] = useState('');
    const [sortConfig, setSortConfig] = useState<{
        key: string;
        direction: 'asc' | 'desc';
    } | null>(null);

    // Filter data based on search term
    const filteredData = useMemo(() => {
        if (!searchTerm) return data;

        const searchableColumns = columns.filter(col => col.searchable !== false);
        
        return data.filter(item =>
            searchableColumns.some(col => {
                const value = getNestedValue(item, col.key as string);
                return String(value).toLowerCase().includes(searchTerm.toLowerCase());
            })
        );
    }, [data, searchTerm, columns]);

    // Sort data
    const sortedData = useMemo(() => {
        if (!sortConfig) return filteredData;

        return [...filteredData].sort((a, b) => {
            const aValue = getNestedValue(a, sortConfig.key);
            const bValue = getNestedValue(b, sortConfig.key);

            if (aValue < bValue) {
                return sortConfig.direction === 'asc' ? -1 : 1;
            }
            if (aValue > bValue) {
                return sortConfig.direction === 'asc' ? 1 : -1;
            }
            return 0;
        });
    }, [filteredData, sortConfig]);

    const handleSort = (key: string) => {
        setSortConfig(current => {
            if (current?.key === key) {
                return {
                    key,
                    direction: current.direction === 'asc' ? 'desc' : 'asc',
                };
            }
            return { key, direction: 'asc' };
        });
    };

    const renderCell = (column: Column<T>, record: T, index: number) => {
        const value = getNestedValue(record, column.key as string);
        
        if (column.render) {
            return column.render(value, record, index);
        }
        
        return value;
    };

    if (loading) {
        return (
            <div className={cn('space-y-4', className)}>
                {searchable && (
                    <div className="flex items-center space-x-2">
                        <Skeleton className="h-10 w-64" />
                    </div>
                )}
                <div className="rounded-md border">
                    <div className="p-4">
                        <div className="space-y-2">
                            {Array.from({ length: 5 }).map((_, i) => (
                                <Skeleton key={i} className="h-12 w-full" />
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className={cn('space-y-4', className)}>
            {searchable && (
                <div className="flex items-center space-x-2">
                    <div className="relative flex-1 max-w-sm">
                        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input
                            placeholder={searchPlaceholder}
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-9"
                        />
                    </div>
                </div>
            )}

            <div className="rounded-md border">
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead>
                            <tr className="border-b bg-muted/50">
                                {columns.map((column, index) => (
                                    <th
                                        key={index}
                                        className={cn(
                                            'px-4 py-3 text-left text-sm font-medium text-muted-foreground',
                                            column.sortable !== false && 'cursor-pointer hover:text-foreground',
                                            column.className
                                        )}
                                        style={{ width: column.width }}
                                        onClick={() => column.sortable !== false && handleSort(column.key as string)}
                                    >
                                        <div className="flex items-center space-x-1">
                                            <span>{column.title}</span>
                                            {column.sortable !== false && sortConfig?.key === column.key && (
                                                <span className="text-xs">
                                                    {sortConfig.direction === 'asc' ? '↑' : '↓'}
                                                </span>
                                            )}
                                        </div>
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody>
                            {sortedData.length === 0 ? (
                                <tr>
                                    <td
                                        colSpan={columns.length}
                                        className="px-4 py-8 text-center text-muted-foreground"
                                    >
                                        {emptyText}
                                    </td>
                                </tr>
                            ) : (
                                sortedData.map((record, index) => (
                                    <tr
                                        key={index}
                                        className="border-b hover:bg-muted/50 transition-colors"
                                    >
                                        {columns.map((column, colIndex) => (
                                            <td
                                                key={colIndex}
                                                className={cn(
                                                    'px-4 py-3 text-sm',
                                                    column.className
                                                )}
                                            >
                                                {renderCell(column, record, index)}
                                            </td>
                                        ))}
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>
            </div>

            {pagination && (
                <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                        Showing {((pagination.current - 1) * pagination.pageSize) + 1} to{' '}
                        {Math.min(pagination.current * pagination.pageSize, pagination.total)} of{' '}
                        {pagination.total} results
                    </div>
                    <div className="flex items-center space-x-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => pagination.onChange(pagination.current - 1, pagination.pageSize)}
                            disabled={pagination.current <= 1}
                        >
                            <ChevronLeft className="h-4 w-4" />
                            Previous
                        </Button>
                        <div className="flex items-center space-x-1">
                            <span className="text-sm">Page</span>
                            <span className="text-sm font-medium">{pagination.current}</span>
                            <span className="text-sm">of</span>
                            <span className="text-sm font-medium">
                                {Math.ceil(pagination.total / pagination.pageSize)}
                            </span>
                        </div>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => pagination.onChange(pagination.current + 1, pagination.pageSize)}
                            disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
                        >
                            Next
                            <ChevronRight className="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            )}
        </div>
    );
}

// Helper function to get nested object values
function getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
}
